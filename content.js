(() => {
  const API_BASE   = location.origin;
  const SHEET_URL  = 'https://script.google.com/macros/s/AKfycbxmQcY3QFnplbl4bL2ljnFmI8I21GtdVOdE3gGUBR3IR-Rif8iyzDfzA3dyeJ3eiEZx/exec';
  const DUAL_ACCOUNT_URL = 'https://script.google.com/macros/s/YOUR_DUAL_ACCOUNT_SCRIPT_ID/exec'; // TODO: Replace with actual script ID
  const PAGE_SIZE  = 24;
  const UND_STEP   = 0.00001;

  // Dual Account Configuration
  const MAIN_ACCOUNT = 'Miba_Shop';
  const SECONDARY_ACCOUNT = 'Bonyadi';
  const MAX_CHANGES_PER_ACCOUNT = 4; // Switch after 4-5 changes
  const RATE_LIMIT_COOLDOWN = 5 * 60 * 1000; // 5 minutes

  const ACTIVE_MAP = {
    'Spineshatter:Alliance': 'Spine A',
    'Spineshatter:Horde':    'Spine H',
    'Thunderstrike:Alliance':'Thunder A',
    'Thunderstrike:Horde':   'Thunder H',
    'Living Flame:Alliance': 'Living A',
    'Living Flame:Horde':    'Living H',
    'Wild Growth:Alliance':  'Wild A',
    'Firemaw:Alliance':      'Firemaw A',
    'Gehennas:Horde':        'Gehennas H',
    'Golemagg:Horde':        'Golemagg H',
    'Venoxis:Horde':         'Venoxis H',
    'Everlook:Alliance':     'Everlook A',
    'Pyrewood Village:Alliance': 'Pyrewood Village A'
  };

  const CATEGORIES = [
    { key:'Retail',      label:'Retail',      params:{gameId:'0', tradeEnv0:['EU'], pageSize:48},                              needsFaction:true, allowedRealms:null      },
    { key:'Anniversary', label:'Anniversary', params:{gameId:'92', apiEndpoint:'EU%20Anniversary'}, needsFaction:true,  allowedRealms:['Thunderstrike','Spineshatter'] },
    { key:'SOD',         label:'SOD',         params:{gameId:'92', apiEndpoint:'EU%20Season%20of%20Discovery'}, needsFaction:true,  allowedRealms:['Living Flame','Wild Growth'] },
    { key:'Cata',        label:'Cata',        params:{gameId:'14', category:'Currency'}, needsFaction:true, allowedRealms:['Firemaw','Gehennas','Golemagg','Venoxis','Everlook','Pyrewood Village'], allowedCombinations: [
      'Firemaw:Alliance',
      'Gehennas:Horde',
      'Golemagg:Horde',
      'Venoxis:Horde',
      'Everlook:Alliance',
      'Pyrewood Village:Alliance'
    ] }
  ];

  let masterInterval = null;
  let sheetData      = [];
  let minRetail      = 0;
  const state = new Map(), UI = {};

  // Dual Account State Management
  const dualAccountState = {
    currentAccount: MAIN_ACCOUNT,
    accountChangeCount: new Map(), // realm:faction -> count
    accountRateLimits: new Map(), // account -> timestamp when rate limit expires
    lastAccountSwitch: new Map(), // realm:faction -> timestamp
    coordinationEnabled: false,
    secondaryAccountOnline: false
  };

  // Global Rate Limiting State - TO BE MOVED TO PER-CATEGORY STATE
  // let isGloballyRateLimited = false; 
  // let rateLimitBackoffUntil = 0;
  // let currentRateLimitBackoffDuration = 5 * 60 * 1000; 
  const MAX_BACKOFF_DURATION = 60 * 60 * 1000; // Max 1 hour backoff

  // At the top of the file, add a cache for "You" prices
  const yourPricesCache = new Map(); // Map of "category:entryId" -> {price, timestamp, needsRefresh}
  
  // Helper function to manage your own prices
  function getYourPrice(category, entryId) {
    const cacheKey = `${category}:${entryId}`;
    return yourPricesCache.get(cacheKey)?.price || null;
  }
  
  function setYourPrice(category, entryId, price) {
    const cacheKey = `${category}:${entryId}`;
    yourPricesCache.set(cacheKey, {
      price,
      timestamp: Date.now(),
      needsRefresh: false
    });
  }
  
  function markPriceForRefresh(category, entryId) {
    const cacheKey = `${category}:${entryId}`;
    const existing = yourPricesCache.get(cacheKey);
    if (existing) {
      existing.needsRefresh = true;
      yourPricesCache.set(cacheKey, existing);
    }
  }
  
  function shouldRefreshPrice(category, entryId) {
    const cacheKey = `${category}:${entryId}`;
    const cached = yourPricesCache.get(cacheKey);
    if (!cached) return true;
    
    // Always refresh if needsRefresh flag is set
    if (cached.needsRefresh) return true;
    
    // Refresh if older than 5 minutes (300,000 ms)
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    if (cached.timestamp < fiveMinutesAgo) return true;
    
    return false;
  }

  // Function to update status display with more informative messages
  function updateStatusDisplay(message) {
    if (UI.statusDisplay) {
      // Store the last error message time to avoid flashing the same message too often
      const now = Date.now();
      UI.statusDisplay.lastErrorTime = UI.statusDisplay.lastErrorTime || 0;
      
      // If this is a network error message, format it differently and track retries
      if (message.includes('Network error') || message.includes('fetching offers')) {
        const category = message.match(/for\s+(\w+)/)?.[1] || '';
        if (category) {
          // Increment retry counter for this category
          UI.statusDisplay.retryCount = UI.statusDisplay.retryCount || {};
          UI.statusDisplay.retryCount[category] = (UI.statusDisplay.retryCount[category] || 0) + 1;
          
          // Format more informative message
          message = `Connectivity issue for ${category} (attempt ${UI.statusDisplay.retryCount[category]}). Will auto-retry...`;
          
          // If we've been retrying for a while, add more context
          if (UI.statusDisplay.retryCount[category] > 3) {
            message += ' Check your internet connection.';
          }
        }
        
        // Don't spam the status area with the same message
        if (now - UI.statusDisplay.lastErrorTime < 5000 && UI.statusDisplay.lastMessage === message) {
          return;
        }
        
        UI.statusDisplay.lastErrorTime = now;
      } else if (message === 'Status: Idle' || message.includes('Monitoring')) {
        // Reset retry counters when we return to normal state
        UI.statusDisplay.retryCount = {};
      }
      
      UI.statusDisplay.textContent = message;
      UI.statusDisplay.title = message; // Show full message on hover
      UI.statusDisplay.lastMessage = message;

      // Also log to enhanced log system
      addToEnhancedLog(message);
    }
  }

  // Enhanced logging system
  function addToEnhancedLog(message, type = 'info') {
    if (!UI.enhancedLog) return;
    
    const now = new Date();
    const timestamp = now.toLocaleTimeString('en-US', { hour12: false }) + '.' + String(now.getMilliseconds()).padStart(3, '0');
    
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.innerHTML = `
      <span class="log-timestamp">${timestamp}</span>
      <span class="log-message">${message}</span>
    `;
    
    // Add the new entry at the top
    UI.enhancedLog.insertBefore(logEntry, UI.enhancedLog.firstChild);
    
    // Limit number of entries to prevent memory issues
    while (UI.enhancedLog.children.length > 200) {
      UI.enhancedLog.removeChild(UI.enhancedLog.lastChild);
    }
    
    // Also store in Chrome storage for persistence
    chrome.storage.local.get('elcutLogs', res => {
      const L = res.elcutLogs || [];
      L.unshift(`${timestamp} - ${message}`);
      // Keep only the last 500 entries
      while (L.length > 500) L.pop();
      chrome.storage.local.set({ elcutLogs: L });
    });
  }

  // Log special events with different styles
  function logInfo(message) {
    addToEnhancedLog(message, 'info');
  }

  function logSuccess(message) {
    addToEnhancedLog(message, 'success');
  }

  function logError(message) {
    addToEnhancedLog(message, 'error');
  }

  function logWarning(message) {
    addToEnhancedLog(message, 'warning');
  }

  // For price change logging
  function logPriceChange(realm, faction, oldPrice, newPrice, category, reason = 'Undercut') {
    const message = `${reason}: ${realm}${faction ? ' '+faction : ''} ${oldPrice ? oldPrice.toFixed(5) : 'N/A'} → ${newPrice.toFixed(5)}`;
    logSuccess(message);
  }

  // Extension context checking
  function checkExtensionContext() {
    try {
      // Simple check - if we can access chrome APIs, context is valid
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  // ───────── DUAL ACCOUNT COORDINATION ─────────────────────────────────

  // Check if we should switch accounts for a realm
  function shouldSwitchAccount(realm, faction) {
    const realmKey = `${realm}:${faction || ''}`;
    const changeCount = dualAccountState.accountChangeCount.get(realmKey) || 0;
    const currentAccount = dualAccountState.currentAccount;

    // Check if current account is rate limited
    const rateLimitExpiry = dualAccountState.accountRateLimits.get(currentAccount);
    if (rateLimitExpiry && Date.now() < rateLimitExpiry) {
      logWarning(`${currentAccount} is rate limited until ${new Date(rateLimitExpiry).toLocaleTimeString()}`);
      return true;
    }

    // Switch after MAX_CHANGES_PER_ACCOUNT changes
    if (changeCount >= MAX_CHANGES_PER_ACCOUNT) {
      logInfo(`Switching account after ${changeCount} changes for ${realmKey}`);
      return true;
    }

    return false;
  }

  // Switch to the other account
  function switchAccount(realm, faction) {
    const realmKey = `${realm}:${faction || ''}`;
    const newAccount = dualAccountState.currentAccount === MAIN_ACCOUNT ? SECONDARY_ACCOUNT : MAIN_ACCOUNT;

    logInfo(`Switching from ${dualAccountState.currentAccount} to ${newAccount} for ${realmKey}`);

    dualAccountState.currentAccount = newAccount;
    dualAccountState.accountChangeCount.set(realmKey, 0); // Reset counter
    dualAccountState.lastAccountSwitch.set(realmKey, Date.now());

    return newAccount;
  }

  // Increment change count for current account
  function incrementAccountChangeCount(realm, faction) {
    const realmKey = `${realm}:${faction || ''}`;
    const currentCount = dualAccountState.accountChangeCount.get(realmKey) || 0;
    dualAccountState.accountChangeCount.set(realmKey, currentCount + 1);

    logInfo(`Account change count for ${realmKey}: ${currentCount + 1}/${MAX_CHANGES_PER_ACCOUNT}`);
  }

  // Report rate limit to coordination system
  async function reportRateLimit(account, realm, faction) {
    const rateLimitExpiry = Date.now() + RATE_LIMIT_COOLDOWN;
    dualAccountState.accountRateLimits.set(account, rateLimitExpiry);

    logWarning(`Rate limit detected for ${account} on ${realm}:${faction || 'N/A'}. Cooldown until ${new Date(rateLimitExpiry).toLocaleTimeString()}`);

    try {
      const params = new URLSearchParams({
        action: 'reportRateLimit',
        account: account,
        realm: realm,
        faction: faction || ''
      });

      await fetch(`${DUAL_ACCOUNT_URL}?${params}`, { method: 'GET' });
    } catch (error) {
      logError(`Failed to report rate limit: ${error.message}`);
    }
  }

  // Request price change from secondary account
  async function requestSecondaryAccountPriceChange(realm, faction, targetPrice, instruction = 'UNDERCUT') {
    try {
      const params = new URLSearchParams({
        action: 'requestPriceChange',
        realm: realm,
        faction: faction || '',
        targetPrice: targetPrice,
        targetAccount: SECONDARY_ACCOUNT,
        instruction: instruction,
        requestedBy: MAIN_ACCOUNT,
        priority: 'normal'
      });

      const response = await fetch(`${DUAL_ACCOUNT_URL}?${params}`, { method: 'GET' });
      const result = await response.json();

      if (result.success) {
        logSuccess(`Price change requested for ${SECONDARY_ACCOUNT} on ${realm}:${faction || 'N/A'} - Target: ${targetPrice}`);
        return true;
      } else {
        logError(`Failed to request price change: ${result.error}`);
        return false;
      }
    } catch (error) {
      logError(`Error requesting secondary account price change: ${error.message}`);
      return false;
    }
  }

  // Check secondary account status
  async function checkSecondaryAccountStatus() {
    try {
      const params = new URLSearchParams({
        action: 'getAccountStatus',
        account: SECONDARY_ACCOUNT
      });

      const response = await fetch(`${DUAL_ACCOUNT_URL}?${params}`, { method: 'GET' });
      const result = await response.json();

      if (result.success) {
        const status = result.accountStatus;
        dualAccountState.secondaryAccountOnline = status.lastActivity &&
          (Date.now() - new Date(status.lastActivity).getTime()) < 2 * 60 * 1000; // Active within 2 minutes

        logInfo(`Secondary account status: ${status.pendingInstructions} pending, ${status.processingInstructions} processing, online: ${dualAccountState.secondaryAccountOnline}`);
        return status;
      }
    } catch (error) {
      logError(`Error checking secondary account status: ${error.message}`);
      dualAccountState.secondaryAccountOnline = false;
    }

    return null;
  }

  // Verify account names and detect which account we're currently using
  async function verifyCurrentAccount() {
    try {
      // Try to get account info from a simple API call
      const response = await fetch(`${API_BASE}/api/predefinedOffers/me/?gameId=0&category=Currency&pageSize=1&pageIndex=1`, {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        // The account name might be in the response or we can infer it from other means
        // For now, we'll assume the main extension is always Miba_Shop
        logInfo(`Account verification: Operating as ${MAIN_ACCOUNT}`);
        return MAIN_ACCOUNT;
      }
    } catch (error) {
      logWarning(`Could not verify account: ${error.message}`);
    }

    return MAIN_ACCOUNT; // Default assumption
  }

  // Check if Bonyadi is correctly undercutting other shops
  async function verifyBonyadiUndercutting(realm, faction) {
    try {
      // Get current global prices for this realm
      const globalPrices = await fetchGlobalPricesForRealm(realm, faction);
      if (!globalPrices || globalPrices.length === 0) {
        logWarning(`Could not fetch global prices for verification: ${realm}:${faction || 'N/A'}`);
        return true; // Assume it's working if we can't verify
      }

      // Find Bonyadi's price and the lowest competitor
      let bonyadiPrice = null;
      let lowestCompetitorPrice = Infinity;
      let lowestCompetitorName = '';

      globalPrices.forEach(offer => {
        const shopName = offer.shopName || offer.seller || '';
        const price = offer.pricePerUnit?.amount || offer.price;

        if (shopName.includes('Bonyadi')) {
          bonyadiPrice = price;
        } else if (price < lowestCompetitorPrice) {
          lowestCompetitorPrice = price;
          lowestCompetitorName = shopName;
        }
      });

      if (bonyadiPrice !== null && lowestCompetitorPrice !== Infinity) {
        const isUndercutting = bonyadiPrice < lowestCompetitorPrice;
        const priceDiff = Math.abs(bonyadiPrice - lowestCompetitorPrice);

        if (isUndercutting) {
          logSuccess(`✓ Bonyadi correctly undercutting ${lowestCompetitorName} by ${priceDiff.toFixed(5)} on ${realm}:${faction || 'N/A'}`);
        } else {
          logWarning(`⚠ Bonyadi NOT undercutting ${lowestCompetitorName} on ${realm}:${faction || 'N/A'} (Bonyadi: ${bonyadiPrice.toFixed(5)}, Competitor: ${lowestCompetitorPrice.toFixed(5)})`);
        }

        return isUndercutting;
      }

      logInfo(`Could not find Bonyadi price for verification on ${realm}:${faction || 'N/A'}`);
      return true; // Assume it's working if we can't find the price

    } catch (error) {
      logError(`Error verifying Bonyadi undercutting: ${error.message}`);
      return true; // Assume it's working on error
    }
  }

  // Fetch global prices for a specific realm (simplified version)
  async function fetchGlobalPricesForRealm(realm, faction) {
    try {
      // Try different game IDs to find the realm
      const gameIds = ['0', '92', '14'];

      for (const gameId of gameIds) {
        try {
          let apiUrl = `${API_BASE}/api/predefinedOffers/game?gameId=${gameId}&category=Currency&pageSize=48&pageIndex=1`;

          if (gameId === '0') {
            apiUrl += '&tradeEnvironmentValue0=EU';
          } else if (gameId === '92') {
            // Try both Anniversary and SOD endpoints
            const endpoints = ['EU%20Anniversary', 'EU%20Season%20of%20Discovery'];
            for (const endpoint of endpoints) {
              try {
                const testUrl = apiUrl + `&tradeEnvironmentValue0=${endpoint}`;
                const response = await fetch(testUrl, { credentials: 'include' });
                if (response.ok) {
                  const data = await response.json();
                  const filtered = (data.results || []).filter(result => {
                    const offer = result.offer || result;
                    const tradeEnvs = offer.tradeEnvironmentValues;
                    return tradeEnvs[1].value === realm && (!faction || tradeEnvs[2].value === faction);
                  });
                  if (filtered.length > 0) return filtered;
                }
              } catch (e) {
                continue;
              }
            }
          } else {
            apiUrl += '&tradeEnvironmentValue0=EU';
          }

          const response = await fetch(apiUrl, { credentials: 'include' });
          if (response.ok) {
            const data = await response.json();
            const filtered = (data.results || []).filter(result => {
              const offer = result.offer || result;
              const tradeEnvs = offer.tradeEnvironmentValues;
              return tradeEnvs[1].value === realm && (!faction || tradeEnvs[2].value === faction);
            });
            if (filtered.length > 0) return filtered;
          }
        } catch (error) {
          continue;
        }
      }

      return null;
    } catch (error) {
      logError(`Error fetching global prices for ${realm}:${faction || 'N/A'}: ${error.message}`);
      return null;
    }
  }

  // Update dual account display in UI
  function updateDualAccountDisplay() {
    if (!UI.currentAccountDisplay || !UI.secondaryStatus) return;

    UI.currentAccountDisplay.textContent = `Current: ${dualAccountState.currentAccount}`;

    if (!dualAccountState.coordinationEnabled) {
      UI.secondaryStatus.textContent = 'Secondary: Disabled';
      UI.secondaryStatus.style.color = '#666';
    } else if (dualAccountState.secondaryAccountOnline) {
      UI.secondaryStatus.textContent = 'Secondary: Online';
      UI.secondaryStatus.style.color = '#28a745';
    } else {
      UI.secondaryStatus.textContent = 'Secondary: Offline';
      UI.secondaryStatus.style.color = '#dc3545';
    }
  }

  // Helper function to handle fetch responses, check for 429s, and parse JSON
  async function handleFetchResponse(response, operationContext, categoryState) {
    // CRITICAL: Never disable categories due to API errors
    // Always preserve category state and use cached data when possible
    
    if (response.status === 429) {
      logWarning(`⚠️ Rate limited during ${operationContext} - using cached data`);
      throw new Error(`HTTP_429:${operationContext}`);
    }
    
    if (response.status >= 500) {
      // Server errors - pause this category temporarily but keep it visible
      logWarning(`⚠️ Server error (${response.status}) during ${operationContext} - using cached data`);
      if (categoryState) {
        categoryState.categoryDataFetchPausedUntil = Date.now() + 2 * 60 * 1000; // Pause for 2 minutes
      }
      throw new Error(`HTTP_${response.status}:${operationContext}`);
    }
    
    if (!response.ok) {
      logWarning(`⚠️ API error during ${operationContext}: ${response.status} - using cached data`);
      throw new Error(`Failed ${operationContext}: ${response.status} ${response.statusText}`);
    }
    
    try {
      return await response.json();
    } catch (e) {
      logWarning(`⚠️ JSON parsing error during ${operationContext} - using cached data`);
      throw new Error(`Failed ${operationContext}: Invalid JSON response`);
    }
  }

  // ───────── SAFETY CHECKS ─────────────────────────────────
  // Track when extension started to be more lenient initially
  const extensionStartTime = Date.now();
  let lastSheetLoadTime = 0;
  
  function canChangePrice() {
    const now = Date.now();
    const fiveMinutesAgo = now - (5 * 60 * 1000);
    const tenMinutesAfterStart = extensionStartTime + (10 * 60 * 1000);
    
    // Be very lenient with Google Sheets - only check if extension has been running for 10+ minutes
    if (now > tenMinutesAfterStart) {
      // Only after 10 minutes of runtime, start checking Google Sheets data
      if (sheetData && sheetData.length > 0 && lastSheetLoadTime > 0) {
        // Allow using stale data for up to 10 minutes - only warn but don't block
        const tenMinutesAgo = now - (10 * 60 * 1000);
        if (lastSheetLoadTime < tenMinutesAgo) {
          logWarning('⚠️ WARNING: Google Sheets data is older than 10 minutes. Using last known data.');
          // Continue working - don't return false here
        }
      } else {
        // Only stop if we have NO Google Sheets data after 10 minutes of running
        logWarning('⚠️ SAFETY STOP: No Google Sheets data available after 10 minutes. Stopping price changes to prevent errors.');
        return false;
      }
    }
    // For the first 10 minutes, allow all price changes regardless of Google Sheets status
    
    // Check if we can reach Eldorado (by checking recent successful API calls)
    const hasRecentSuccess = UI.statusDisplay && 
      UI.statusDisplay.lastMessage && 
      !UI.statusDisplay.lastMessage.includes('Connectivity issue') &&
      !UI.statusDisplay.lastMessage.includes('Network error');
    
    if (!hasRecentSuccess && UI.statusDisplay && UI.statusDisplay.retryCount) {
      const totalRetries = Object.values(UI.statusDisplay.retryCount).reduce((sum, count) => sum + count, 0);
      if (totalRetries > 5) {
        logWarning('⚠️ SAFETY STOP: Multiple network errors detected. Stopping price changes until connectivity improves.');
        return false;
      }
    }
    
    return true;
  }

  // ───────── UTILITY ───────────────────────────────────────
  function filterUnderList(cat) {
    const st = state.get(cat.key);
    if (!st) return;
    const L = [];
    for (const [key, row] of st.rows) {
      const under = row.tr.classList.contains('highlight');
      if (under && row.skipBox.checked && !row.skipBox.disabled) {
        L.push({ entryId: key.split(':')[1] });
      }
    }
    st.underList = L;
    st.btnUnder.disabled = L.length === 0;
  }
  


  // ───────── PANEL & STYLES ─────────────────────────────────
  const style = document.createElement('style');
  style.textContent = `
    #elcut-panel { max-height:90vh; overflow:auto; max-width:95vw; }
    #tables-container { display:flex; flex-wrap:wrap; gap:12px; }
    .category-row { display:flex; gap:12px; margin-bottom:12px; width:100%; }
    .category-container { flex:1; }
    .retail-container { width:100%; display: flex; flex-wrap: wrap; }
    .retail-section { flex: 1; min-width: 300px; margin-right: 10px; }
    .highlight { background:#fffbcc!important }
    .below-threshold { background:#ffe6e6!important }
    .muted { opacity:0.4 }
    .inactive-flag { background:#ffe8cc!important }
    table { width:100%; border-collapse:collapse }
    th,td { padding:4px; border-bottom:1px solid #eee; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; }
    .local { color:green }
    .skip-cell { text-align:center }
    .select-all { cursor:pointer }
    #global-info { font-size:12px; color:#333; margin-left:16px; }
    #sync-status { margin-left:16px; font-size:12px; color:#555; }
    
    /* "You" price freshness indicators */
    .you-price-fresh { color: #28a745; font-weight: bold; }
    .you-price-stale { color: #dc3545; font-style: italic; }
    .you-price-loading { color: #6c757d; opacity: 0.7; }
    
    /* Clickable prices */
    .global-price-clickable, .you-price-clickable { 
      cursor: pointer; 
      user-select: none;
      transition: background-color 0.2s ease;
    }
    .global-price-clickable:hover, .you-price-clickable:hover { 
      background-color: #e3f2fd; 
      border-radius: 3px;
    }
    .global-price-clickable:active, .you-price-clickable:active { 
      background-color: #bbdefb; 
    }
    
    /* Different muted states with distinct colors - high specificity */
    .muted { opacity:0.4 }
    tr.muted-too-many-failures { background-color: #ffebee !important; opacity: 0.8; }
    tr.muted-below-min { background-color: #fff3e0 !important; opacity: 0.8; }
    tr.muted-manual-price { background-color: #f3e5f5 !important; opacity: 0.9; }
    tr.fallback-price { background-color: #e8f5e8 !important; opacity: 0.9; }
    
    /* Ensure manual price inputs are always enabled */
    .manual-price-input, .manual-price-btn {
      opacity: 1 !important;
      pointer-events: auto !important;
    }
    
    /* Visual indicator that manual pricing always works */
    .muted-too-many-failures .manual-price-input::placeholder,
    .muted-below-min .manual-price-input::placeholder {
      content: "Manual OK";
    }
    
    /* Subtle indicator for unchecked realms (doesn't conflict with error states) */
    tr.unchecked {
      border-left: 3px solid #ddd;
    }
    
    /* New styles for realm-level controls */
    .profit-pct-input, .manual-price-input {
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 2px 4px;
      font-family: inherit;
    }
    .profit-pct-input:focus, .manual-price-input:focus {
      border-color: #4CAF50;
      outline: none;
    }
    .manual-price-btn {
      background: #4CAF50;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-weight: bold;
    }
    .manual-price-btn:hover {
      background: #45a049;
    }
    .manual-price-btn:disabled {
      background: #cccccc;
      cursor: not-allowed;
    }
    
    /* Competitor UI styles */
    .competitor-overlay {
      font-family: inherit;
    }
    .competitor-overlay h3 {
      margin: 0 0 15px 0;
      font-size: 18px;
    }
    .competitor-overlay p {
      margin: 0 0 20px 0;
      line-height: 1.4;
    }
    .undercut-competitor-btn:hover {
      background: #449d44 !important;
    }
    .undercut-competitor-btn:disabled {
      background: #cccccc !important;
      cursor: not-allowed !important;
    }
    
    /* Enhanced log styles */
    #enhanced-log-container { 
      border: 1px solid #ddd; 
      border-radius: 6px; 
      background: #f9f9f9; 
      margin-top: 10px; 
      display: flex;
      flex-direction: column;
      height: 200px;
      transition: height 0.3s ease;
    }
    #enhanced-log-container.minimized {
      height: 36px;
      overflow: hidden;
    }
    #enhanced-log-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 10px;
      background: #eee;
      border-bottom: 1px solid #ddd;
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
    }
    #enhanced-log-title {
      font-weight: bold;
    }
    #enhanced-log-controls {
      display: flex;
      gap: 5px;
    }
    #enhanced-log {
      flex: 1;
      overflow-y: auto;
      padding: 5px;
      font-family: monospace;
      font-size: 12px;
      height: 100%;
    }
    .log-entry {
      padding: 2px 5px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
    }
    .log-entry:hover {
      background-color: #f0f0f0;
    }
    .log-timestamp {
      color: #888;
      margin-right: 10px;
      flex-shrink: 0;
    }
    .log-message {
      white-space: pre-wrap;
      word-break: break-word;
    }
    .log-info {
      color: #333;
    }
    .log-success {
      color: #28a745;
    }
    .log-warning {
      color: #ffc107;
    }
    .log-error {
      color: #dc3545;
    }
    .log-filter {
      margin-left: 5px;
    }
    .log-filter-active {
      background-color: #e6f7ff;
      border-color: #1890ff;
    }
    #log-search {
      margin-left: 10px;
      padding: 2px 5px;
      border: 1px solid #ddd;
      border-radius: 3px;
    }
    #log-clear {
      margin-left: 5px;
    }
  `;
  document.head.appendChild(style);

  // ───────── PANEL SETUP ────────────────────────────────────
  function createPanel() {
    const p = document.createElement('div');
    p.id = 'elcut-panel';
    Object.assign(p.style, {
      position:'fixed', top:'20px', left:'20px',
      background:'#fff', border:'1px solid #ccc', borderRadius:'8px',
      boxShadow:'0 2px 6px rgba(0,0,0,0.2)', fontFamily:'sans-serif',
      fontSize:'14px', userSelect:'none', minWidth:'720px', zIndex:9999
    });
    p.innerHTML = `
      <div id="title-bar" style="cursor:move;padding:8px;background:#f0f0f0;display:flex;align-items:center">
        <strong>El-cut Price Comparator</strong>
        <span id="global-info">$/Toman: –   Min E.Kaf: –</span>
        <span id="sync-status">Last sync: –</span>
        <div style="margin-left:auto;display:flex;align-items:center">
          Delay:
          <div style="display:flex;gap:5px;align-items:center;margin-bottom:5px">
            <input id="min-delay" type="number" min="0.1" step="0.1" value="3" style="width:50px">
            <span>to</span>
            <input id="max-delay" type="number" min="0.1" step="0.1" value="6" style="width:50px">
            <span>sec delay</span>
          </div>
        </div>
      </div>
      <div id="body" style="padding:8px">
        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:8px">
          <div id="toggles"></div>
          <div id="dual-account-controls" style="display:flex;align-items:center;gap:10px;font-size:12px">
            <label>
              <input type="checkbox" id="enable-dual-account"/> Enable Dual Account
            </label>
            <span id="current-account-display">Current: ${MAIN_ACCOUNT}</span>
            <span id="secondary-status">Secondary: Offline</span>
            <button id="check-secondary-status" style="padding:2px 6px">Check Status</button>
          </div>
        </div>
        <div style="margin-bottom:8px">
          <button id="show-log" style="display:none">Show Legacy Log</button>
        </div>
        <div id="status-container" style="margin-bottom:8px; padding: 4px; border: 1px solid #eee; min-height: 20px; font-size: 0.9em; background: #f9f9f9;">
            <span id="status-display">Status: Idle</span>
        </div>
        <div style="margin-bottom:8px">
          Master Interval (sec):
          <input id="elcut-interval-input" type="number" step="1" style="width:60px"/>
        </div>
        <div id="tables-container" style="overflow:auto"></div>
        
        <!-- Enhanced Log UI -->
        <div id="enhanced-log-container">
          <div id="enhanced-log-header">
            <div id="enhanced-log-title">Activity Log</div>
            <div id="enhanced-log-controls">
              <input type="text" id="log-search" placeholder="Search logs..." />
              <button class="log-filter" data-type="all">All</button>
              <button class="log-filter" data-type="info">Info</button>
              <button class="log-filter" data-type="success">Success</button>
              <button class="log-filter" data-type="warning">Warning</button>
              <button class="log-filter" data-type="error">Error</button>
              <button id="log-clear">Clear</button>
              <button id="log-export">Export</button>
              <button id="log-minimize">▼</button>
            </div>
          </div>
          <div id="enhanced-log"></div>
        </div>
      </div>
    `;
    document.body.appendChild(p);

    UI.titleBar   = p.querySelector('#title-bar');
    UI.globalInfo = p.querySelector('#global-info');
    UI.syncStatus = p.querySelector('#sync-status');
    UI.minDelay   = p.querySelector('#min-delay');
    UI.maxDelay   = p.querySelector('#max-delay');
    UI.toggles    = p.querySelector('#toggles');
    UI.showLog    = p.querySelector('#show-log');
    UI.statusDisplay = p.querySelector('#status-display');
    UI.intervalInput = p.querySelector('#elcut-interval-input');
    UI.tables     = p.querySelector('#tables-container');
    UI.enhancedLog = p.querySelector('#enhanced-log');
    UI.logSearch = p.querySelector('#log-search');
    UI.logClear = p.querySelector('#log-clear');
    UI.logExport = p.querySelector('#log-export');
    UI.logFilters = p.querySelectorAll('.log-filter');

    // Dual account UI elements
    UI.enableDualAccount = p.querySelector('#enable-dual-account');
    UI.currentAccountDisplay = p.querySelector('#current-account-display');
    UI.secondaryStatus = p.querySelector('#secondary-status');
    UI.checkSecondaryStatus = p.querySelector('#check-secondary-status');

    // Modify the tables container to organize categories
    UI.tablesRow1 = document.createElement('div');
    UI.tablesRow1.className = 'category-row';
    UI.tablesRow2 = document.createElement('div');
    UI.tablesRow2.className = 'category-row';
    UI.tables.appendChild(UI.tablesRow1);
    UI.tables.appendChild(UI.tablesRow2);

    // Restore inputs
    const defaultIntervalSeconds = 30;
    UI.intervalInput.value = localStorage.getItem('elcut_interval') || defaultIntervalSeconds.toString();
    // Ensure a valid value is stored initially if one wasn't there or was invalid
    if (isNaN(parseInt(UI.intervalInput.value, 10)) || parseInt(UI.intervalInput.value, 10) < 5) {
        UI.intervalInput.value = defaultIntervalSeconds.toString();
    }
    localStorage.setItem('elcut_interval', UI.intervalInput.value);

    UI.minDelay.value = localStorage.getItem('elcut_minDelay') || '3';
    UI.maxDelay.value = localStorage.getItem('elcut_maxDelay') || '6';
    
    // Keep step as 0.1 for decimal support (don't override the HTML step="0.1")
    // UI.minDelay.step = '1';
    // UI.maxDelay.step = '1';

    // Prevent panel collapse on double-click in inputs
    UI.minDelay.addEventListener('dblclick', (e) => {
      e.stopPropagation();
    });
    UI.maxDelay.addEventListener('dblclick', (e) => {
      e.stopPropagation();
    });
    UI.intervalInput.addEventListener('dblclick', (e) => {
      e.stopPropagation();
    });

    UI.minDelay.addEventListener('change', ()=>{
      // Support decimal values with minimum of 0.1
      let val = parseFloat(UI.minDelay.value);
      if(isNaN(val) || val < 0.1) val = 0.1;
      UI.minDelay.value = val.toString();
      localStorage.setItem('elcut_minDelay', UI.minDelay.value);
    });
    
    UI.maxDelay.addEventListener('change', ()=>{
      // Support decimal values
      let val = parseFloat(UI.maxDelay.value);
      if(isNaN(val) || val < parseFloat(UI.minDelay.value)) val = parseFloat(UI.minDelay.value);
      UI.maxDelay.value = val.toString();
      localStorage.setItem('elcut_maxDelay', UI.maxDelay.value);
    });

    UI.intervalInput.addEventListener('change', ()=>{
      let val = parseInt(UI.intervalInput.value, 10);
      if (isNaN(val) || val < 5) {
        val = 30; // Default to 30 seconds if input is too low or invalid
      }
      UI.intervalInput.value = val.toString();
      localStorage.setItem('elcut_interval', UI.intervalInput.value);
      
      // If masterInterval is running and there are active categories, restart it
      if (masterInterval && state.size > 0) {
        console.log('Master interval changed. Restarting with new value...');
        clearInterval(masterInterval);
        masterInterval = null; // Set to null before calling start, so it truly restarts
        startMasterIntervalIfNeeded(); 
      }
    });

    // Setup log filter functionality
    UI.logFilters.forEach(btn => {
      btn.addEventListener('click', () => {
        const filterType = btn.dataset.type;
        
        // Reset all buttons
        UI.logFilters.forEach(b => b.classList.remove('log-filter-active'));
        
        // Activate this button
        btn.classList.add('log-filter-active');
        
        // Apply filter
        const entries = UI.enhancedLog.querySelectorAll('.log-entry');
        entries.forEach(entry => {
          if (filterType === 'all') {
            entry.style.display = '';
          } else {
            entry.style.display = entry.classList.contains(`log-${filterType}`) ? '' : 'none';
          }
        });
      });
    });
    
    // Set "All" as the default active filter
    UI.logFilters[0].classList.add('log-filter-active');
    
    // Setup log search
    UI.logSearch.addEventListener('input', () => {
      const searchTerm = UI.logSearch.value.toLowerCase();
      const entries = UI.enhancedLog.querySelectorAll('.log-entry');
      
      entries.forEach(entry => {
        const messageText = entry.querySelector('.log-message').textContent.toLowerCase();
        entry.style.display = messageText.includes(searchTerm) ? '' : 'none';
      });
    });
    
    // Setup log clear
    UI.logClear.addEventListener('click', () => {
      UI.enhancedLog.innerHTML = '';
      chrome.storage.local.set({ elcutLogs: [] });
      addToEnhancedLog('Log cleared');
    });
    
    // Setup log export
    UI.logExport.addEventListener('click', () => {
      chrome.storage.local.get('elcutLogs', res => {
        const logs = res.elcutLogs || [];
        const blob = new Blob([logs.join('\n')], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `elcut-logs-${new Date().toISOString().replace(/:/g, '-')}.txt`;
        a.click();
        URL.revokeObjectURL(url);
        addToEnhancedLog('Logs exported to file');
      });
    });
    
    // Setup log minimize
    UI.logMinimize = p.querySelector('#log-minimize');
    UI.logContainer = p.querySelector('#enhanced-log-container');
    UI.logMinimize.addEventListener('click', () => {
      UI.logContainer.classList.toggle('minimized');
      UI.logMinimize.textContent = UI.logContainer.classList.contains('minimized') ? '▲' : '▼';
    });
    
    // Show legacy log
    UI.showLog.addEventListener('click', ()=>{
      window.open(chrome.runtime.getURL('log.html'),'ElCutLog','width=400,height=500');
    });

    // Dual account controls
    UI.enableDualAccount.addEventListener('change', () => {
      dualAccountState.coordinationEnabled = UI.enableDualAccount.checked;
      localStorage.setItem('elcut_dualAccountEnabled', dualAccountState.coordinationEnabled.toString());

      if (dualAccountState.coordinationEnabled) {
        logInfo('Dual account coordination enabled');
        checkSecondaryAccountStatus();
      } else {
        logInfo('Dual account coordination disabled');
        UI.secondaryStatus.textContent = 'Secondary: Disabled';
      }

      updateDualAccountDisplay();
    });

    UI.checkSecondaryStatus.addEventListener('click', async () => {
      UI.checkSecondaryStatus.disabled = true;
      UI.checkSecondaryStatus.textContent = 'Checking...';

      await checkSecondaryAccountStatus();
      updateDualAccountDisplay();

      UI.checkSecondaryStatus.disabled = false;
      UI.checkSecondaryStatus.textContent = 'Check Status';
    });

    // Restore dual account setting
    const savedDualAccountEnabled = localStorage.getItem('elcut_dualAccountEnabled');
    if (savedDualAccountEnabled === 'true') {
      UI.enableDualAccount.checked = true;
      dualAccountState.coordinationEnabled = true;
      checkSecondaryAccountStatus();
    }

    updateDualAccountDisplay();

    makeDraggable(p, UI.titleBar);
    UI.titleBar.addEventListener('dblclick', ()=>{
      const b = p.querySelector('#body');
      b.style.display = b.style.display==='none'?'block':'none';
    });
    
    // Load any existing logs from storage
    chrome.storage.local.get('elcutLogs', res => {
      const logs = res.elcutLogs || [];
      logs.slice(0, 50).forEach(log => {
        const parts = log.split(' - ');
        const timestamp = parts[0];
        const message = parts.slice(1).join(' - ');
        
        // Determine log type based on content
        let type = 'info';
        if (message.includes('Error') || message.includes('Failed')) {
          type = 'error';
        } else if (message.includes('Warning')) {
          type = 'warning';
        } else if (message.includes('→')) {
          type = 'success';
        }
        
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        logEntry.innerHTML = `
          <span class="log-timestamp">${timestamp}</span>
          <span class="log-message">${message}</span>
        `;
        
        UI.enhancedLog.appendChild(logEntry);
      });
    });
  }

  function makeDraggable(panel, handle) {
    let dx, dy, dragging = false;
    
    handle.addEventListener('mousedown', e => {
      dragging = true;
      const r = panel.getBoundingClientRect();
      dx = e.clientX - r.left; 
      dy = e.clientY - r.top;
    });
    
    document.addEventListener('mousemove', e => {
      if (!dragging) return;
      
      // Calculate new position
      let newLeft = e.clientX - dx;
      let newTop = e.clientY - dy;
      
      // Get panel dimensions and viewport dimensions
      const panelRect = panel.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      // Apply bounds checking
      // Don't let panel go completely off-screen (keep at least 50px visible)
      newLeft = Math.max(-panelRect.width + 50, newLeft);
      newLeft = Math.min(viewportWidth - 50, newLeft);
      
      newTop = Math.max(0, newTop); // Don't go above viewport
      newTop = Math.min(viewportHeight - 50, newTop); // Keep at least title bar visible
      
      panel.style.left = `${newLeft}px`;
      panel.style.top = `${newTop}px`;
    });
    
    document.addEventListener('mouseup', () => dragging = false);
  }

  // ───────── CATEGORY TOGGLES ─────────────────────────────────
  function renderToggles() {
    CATEGORIES.forEach(cat=>{
      const cb = document.createElement('input');
      cb.type='checkbox'; cb.id=`cb-${cat.key}`;
      const lb = document.createElement('label');
      lb.htmlFor=cb.id; lb.textContent=cat.label;
      lb.style.marginRight='16px';
      cb.addEventListener('change', async ()=>{
        if(cb.checked) {
          initCategory(cat);
          
          // For Retail, populate with cached data immediately to show category instantly
          if (cat.key === 'Retail') {
            logInfo(`${cat.label} category enabled - showing immediately with cached data...`);
            
            // Create dummy entries from cache to show the category structure immediately
            const st = state.get(cat.key);
            if (st) {
              // Show empty sections first for immediate feedback
              st.sections.forEach((section, index) => {
                if (section.children.length === 0) {
                  const emptyRow = document.createElement('tr');
                  emptyRow.innerHTML = `
                    <td class="skip-cell">⏳</td>
                    <td colspan="4" style="text-align:center;color:#666;font-style:italic">Loading realms...</td>
                  `;
                  section.appendChild(emptyRow);
                }
              });
            }
            
            // Fetch fresh data in background without blocking UI (short delay)
            setTimeout(() => {
              logInfo(`${cat.label} - fetching fresh data in background...`);
              fetchAndCompare(cat);
            }, 50);
          } else {
            // For other categories, show immediately with cached data
            logInfo(`${cat.label} category enabled - showing with cached data...`);
            
            // Fetch fresh data in background without blocking UI
            setTimeout(() => {
              logInfo(`${cat.label} - fetching fresh data in background...`);
              fetchAndCompare(cat);
            }, 100);
          }
          
          startMasterIntervalIfNeeded();
        } else {
          const st = state.get(cat.key);
          if (st && st.autoRunning) {
            toggleAuto(cat, st.btnAuto);
          }
          teardownCategory(cat);
          stopMasterIntervalIfNoCategoriesActive();
        }
      });
      UI.toggles.append(cb, lb);
    });
  }

  function initCategory(cat) {
    if(state.has(cat.key)) return;
    const w = document.createElement('div');
    w.className = cat.key === 'Retail' ? 'retail-container' : 'category-container';
    
    if (cat.key === 'Retail') {
      // For Retail, create 4 column layout with a single select-all at the top
      // Add manual price setting controls for Retail
      w.innerHTML = `
        <div style="display:flex;justify-content:space-between;align-items:center;width:100%">
          <div style="display:flex;align-items:center">
            <strong>${cat.label}</strong>
            <label style="margin-left:12px">
              <input type="checkbox" class="select-all-retail"/> Select All
            </label>
          </div>
          <div style="display:flex;align-items:center;gap:8px">
            <input id="retail-manual-price-${cat.key}" type="number" step="0.00001" placeholder="Manual Price (USD)" style="width:120px;font-size:12px"/>
            <button id="retail-set-price-${cat.key}" style="padding:2px 6px;font-size:12px">✓</button>
            <button id="under-${cat.key}" disabled>Undercut</button>
            <button id="auto-${cat.key}" style="margin-left:4px">Auto</button>
          </div>
        </div>
        <div class="retail-sections-container" style="display:flex;flex-wrap:wrap;width:100%">
          <!-- Will be filled dynamically -->
        </div>
      `;
    } else {
      // For other categories, remove the category-level Min % input
    w.innerHTML = `
      <div style="display:flex;justify-content:space-between;align-items:center">
          <strong>${cat.label}</strong>
        <div>
          <button id="under-${cat.key}" disabled>Undercut</button>
          <button id="auto-${cat.key}" style="margin-left:4px">Auto</button>
        </div>
      </div>
      <table>
        <thead><tr>
          <th class="skip-cell"><input type="checkbox" class="select-all"/></th>
          <th>Realm</th>
          ${cat.needsFaction?'<th>Faction</th>':''}
          <th style="text-align:right">Global</th>
          <th style="text-align:right">You</th>
            <th style="text-align:right">Local</th>
            <th style="text-align:right;width:80px">Profit%</th>
            <th style="text-align:right;width:100px">Manual</th>
        </tr></thead>
        <tbody id="tb-${cat.key}"></tbody>
      </table>
    `;
    }
    
    Object.assign(w.style,{border:'1px solid #ddd',borderRadius:'6px',padding:'8px',background:'#fff'});
    
    // Add to the correct container based on category
    if (cat.key === 'Retail') {
      UI.tablesRow2.appendChild(w);
    } else {
      UI.tablesRow1.appendChild(w);
    }

    const btnUnder = w.querySelector(`#under-${cat.key}`);
    const btnAuto  = w.querySelector(`#auto-${cat.key}`);
    
    if (cat.key === 'Retail') {
      // For Retail, create 4 sections for realms
      const retailSectionsContainer = w.querySelector('.retail-sections-container');
      const selectAllRetail = w.querySelector('.select-all-retail');
      const retailManualPriceInput = w.querySelector(`#retail-manual-price-${cat.key}`);
      const retailSetPriceBtn = w.querySelector(`#retail-set-price-${cat.key}`);
      
      // Create 4 sections for Retail realms (12 realms per section)
      for (let i = 0; i < 4; i++) {
        const section = document.createElement('div');
        section.className = 'retail-section';
        section.innerHTML = `
          <table>
            <thead><tr>
              <th class="skip-cell"></th>
              <th>Realm</th>
              <th>Faction</th>
              <th style="text-align:right">Global</th>
              <th style="text-align:right">You</th>
            </tr></thead>
            <tbody id="tb-${cat.key}-section-${i}"></tbody>
          </table>
        `;
        retailSectionsContainer.appendChild(section);
      }
      
      // Add event listener for the single select-all checkbox
      selectAllRetail.addEventListener('change', () => {
        const v = selectAllRetail.checked;
        w.querySelectorAll('.skip').forEach(cb => cb.checked = v);
        // Don't apply basic muted class - let specific error states show instead
        filterUnderList(cat);
      });

      // Add helpful tooltips for Retail manual pricing
      retailManualPriceInput.title = "Manual pricing always works - no restrictions";
      retailSetPriceBtn.title = "Set manual price for all checked realms - bypasses all restrictions";

      // Add event listener for retail manual price setting
      retailSetPriceBtn.addEventListener('click', async () => {
        const st = state.get(cat.key); // Get current state reference
        const priceValue = parseFloat(retailManualPriceInput.value);
        if (isNaN(priceValue) || priceValue <= 0) {
          alert('Please enter a valid price in USD');
          return;
        }
        
        // Get all checked realms
        const checkedRows = Array.from(st.rows.values()).filter(row => row.skipBox.checked);
        if (checkedRows.length === 0) {
          alert('Please select at least one realm');
          return;
        }

        retailSetPriceBtn.disabled = true;
        retailSetPriceBtn.textContent = '...';
        
        let successCount = 0;
        let totalCount = checkedRows.length;
        
        // Set prices for all checked realms with delay
        for (let i = 0; i < checkedRows.length; i++) {
          const row = checkedRows[i];
          const en = row.entryRef;
          
          if (await setManualPrice(cat, en, row, priceValue)) {
            successCount++;
            logSuccess(`Manual price set for ${en.realm}${en.faction ? ` ${en.faction}` : ''} = ${priceValue.toFixed(5)}`);
            
            // Mark as manually priced to prevent auto processing
            row.manuallyPriced = true;
            row.manualPriceTimestamp = Date.now();
            
            // Uncheck the realm so auto won't override the manual price
            row.skipBox.checked = false;
            row.tr.classList.remove('muted', 'muted-too-many-failures', 'muted-below-min');
            row.tr.classList.add('muted-manual-price');
          }
          
          // Add delay between setting prices (except for the last one)
          if (i < checkedRows.length - 1) {
            await new Promise(resolve => setTimeout(resolve, randDelay() * 1000));
          }
        }
        
        retailSetPriceBtn.disabled = false;
        retailSetPriceBtn.textContent = '✓';
        
        // Wait for all prices to be set, then refresh "You" prices once
        setTimeout(() => {
          logInfo(`Verifying prices for ${totalCount} Retail realms...`);
          fetchAndCompare(cat);
        }, 5000);
      });
      
    } else {
      // For other categories, handle the select-all checkbox as before
      const chkAll = w.querySelector('.select-all');
      chkAll.addEventListener('change', () => {
        const v = chkAll.checked;
        w.querySelectorAll('.skip').forEach(cb => cb.checked = v);
        // Don't apply basic muted class - let specific error states show instead
        filterUnderList(cat);
      });
    }
    
    btnUnder.addEventListener('click', () => manualUndercut(cat));
    btnAuto.addEventListener('click', () => {
        const st = state.get(cat.key);
        if (st) {
            toggleAuto(cat, st.btnAuto);
        }
    });

    const stateObj = {
      wrapper: w,
      btnUnder, btnAuto,
      rows: new Map(),
      underList: [],
      autoRunning: false,
      realmRetryAttempts: new Map(),
      key: cat.key
    };

    // For Retail, add references to each section's tbody and competitor monitoring
    if (cat.key === 'Retail') {
      stateObj.sections = [];
      for (let i = 0; i < 4; i++) {
        stateObj.sections[i] = w.querySelector(`#tb-${cat.key}-section-${i}`);
      }
      stateObj.selectAllRetail = w.querySelector('.select-all-retail');
      stateObj.retailManualPriceInput = w.querySelector(`#retail-manual-price-${cat.key}`);
      stateObj.retailSetPriceBtn = w.querySelector(`#retail-set-price-${cat.key}`);
      // Competitor monitoring properties
      stateObj.competitorMonitoringInterval = null;
      stateObj.competitorUIVisible = false;
    } else {
      // For other categories, keep using the single tbody
      stateObj.tbody = w.querySelector(`#tb-${cat.key}`);
    }

    state.set(cat.key, stateObj);
  }

  function teardownCategory(cat) {
    const st = state.get(cat.key);
    if(!st) return;
    // Ensure auto-undercutting for this category is stopped
    if (st.autoRunning) {
        toggleAuto(cat, st.btnAuto); // This calls clearTimeout for the category-specific loop
    }
    // Clean up competitor monitoring for Retail
    if (cat.key === 'Retail' && st.competitorMonitoringInterval) {
      clearInterval(st.competitorMonitoringInterval);
    }
    if (cat.key === 'Retail') {
      UI.tablesRow2.removeChild(st.wrapper);
    } else {
      UI.tablesRow1.removeChild(st.wrapper);
    }
    state.delete(cat.key);
  }

  // ───────── MASTER RUN ────────────────────────────────────
  async function masterRun() {
    await loadSheet();
    state.forEach((_, key)=>{
      const cat = CATEGORIES.find(c=>c.key===key);
      fetchAndCompare(cat);
    });
  }

  // ───────── SHEET & GLOBAL INFO ───────────────────────────
  async function loadSheet(){
    try {
      const r = await fetch(SHEET_URL);
      sheetData = await r.json();
      lastSheetLoadTime = Date.now(); // Track successful sheet load time
    } catch {
      sheetData = [];
      // Don't update lastSheetLoadTime on failure
    }
    minRetail = (sheetData.find(i=>i.realm==='Eldorado Kaf')||{}).price||0;
    const dollar = (sheetData.find(i=>i.realm==='Dollar')||{}).price||0;
    UI.globalInfo.textContent = `$/Toman: ${dollar}   Min E.Kaf: ${minRetail}`;
    UI.syncStatus.textContent = `Last sync: ${new Date().toLocaleTimeString()}`;
  }

  // ───────── FETCH & RENDER ───────────────────────────────
  async function fetchAndCompare(cat){
    const st = state.get(cat.key);
    if(!st) return;

    let categoryDataFetchPausedUntil = st.categoryDataFetchPausedUntil || 0;
    if (Date.now() < categoryDataFetchPausedUntil) {
      updateStatusDisplay(`Data fetch for ${cat.key} paused. Retrying in ~${Math.round((categoryDataFetchPausedUntil - Date.now())/1000)}s.`);
      return; // Skip fetching data for this category this cycle
    }

    // 1) fetch global
    let all=[];
    
    // Cache the global prices separately from our own prices to prevent confusion
    if (!st.globalPrices) {
      st.globalPrices = new Map(); // Realm:Faction -> global price
    }
    
    // For Retail, use the existing approach with multiple API calls
    if (cat.key === 'Retail') {
    for(const region of cat.params.tradeEnv0){
      try {
          const u = new URL('/api/predefinedOffers/game/',API_BASE);
        u.searchParams.set('gameId',cat.params.gameId);
        u.searchParams.set('category','Currency');
          u.searchParams.set('pageSize', cat.params.pageSize || PAGE_SIZE);
        u.searchParams.set('pageIndex','1');
        u.searchParams.set('tradeEnvironmentValue0',region);
          
          let r;
          try {
              r = await fetch(u,{credentials:'include'});
          } catch (networkError) {
              console.warn(`Network error fetching global offers for ${cat.key} - ${region}:`, networkError);
              updateStatusDisplay(`Network error fetching offers for ${cat.key}. Check console.`);
              continue;
          }
          
          const regionOfferData = await handleFetchResponse(r, `fetching global offers for ${cat.key} - ${region}`, st);
          
          // Store global prices in cache first
          (regionOfferData.results || []).forEach(e => {
            const o = e.offer || e, t = o.tradeEnvironmentValues;
            const realm = t[1].value;
            const faction = cat.needsFaction ? t[2].value : null;
            const key = `${realm}:${faction || ''}`;
            const globalPrice = o.pricePerUnit.amount;
            st.globalPrices.set(key, globalPrice);
          });
          
          all.push(...(regionOfferData.results || []));
        } catch (error) {
          if (error.message.startsWith('HTTP_429')) {
              updateStatusDisplay(`Data fetch for ${cat.key} hit 429. Pausing for 1 min.`);
              st.categoryDataFetchPausedUntil = Date.now() + 1 * 60 * 1000;
              return; 
          }
          console.error(`Error fetching global offers for ${cat.key} - ${region}. Details:`, error);
        }
      }
    } 
    // For Anniversary and SOD, use the new direct API endpoints
    else if (cat.params.apiEndpoint) {
      try {
        const apiUrl = `${API_BASE}/api/predefinedOffers/game?gameId=${cat.params.gameId}&category=Currency&pageSize=${PAGE_SIZE}&pageIndex=1&tradeEnvironmentValue0=${cat.params.apiEndpoint}`;
        
        let r;
        try {
          r = await fetch(apiUrl, {credentials:'include'});
        } catch (networkError) {
          console.warn(`Network error fetching global offers for ${cat.key}:`, networkError);
          updateStatusDisplay(`Network error fetching offers for ${cat.key}. Check console.`);
          return;
        }
        
        const offerData = await handleFetchResponse(r, `fetching global offers for ${cat.key}`, st);
        all.push(...(offerData.results || []));
      } catch (error) {
        if (error.message.startsWith('HTTP_429')) {
          updateStatusDisplay(`Data fetch for ${cat.key} hit 429. Pausing for 1 min.`);
          st.categoryDataFetchPausedUntil = Date.now() + 1 * 60 * 1000;
          return;
        }
        console.error(`Error fetching global offers for ${cat.key}. Details:`, error);
        return;
      }
    }
    // For Cata category
    else if (cat.key === 'Cata') {
      try {
        // Construct the API URL explicitly without using template literals to avoid any encoding issues
        const apiUrl = API_BASE + "/api/predefinedOffers/game";
        const url = new URL(apiUrl);
        url.searchParams.set('gameId', cat.params.gameId);
        url.searchParams.set('category', 'Currency');
        url.searchParams.set('pageSize', PAGE_SIZE.toString());
        url.searchParams.set('pageIndex', '1');
        url.searchParams.set('tradeEnvironmentValue0', 'EU');
        
        console.log(`Cata API URL: ${url.toString()}`);
        
        let r;
        try {
          r = await fetch(url, {credentials:'include'});
        } catch (networkError) {
          console.warn(`Network error fetching global offers for ${cat.key}:`, networkError);
          updateStatusDisplay(`Network error fetching offers for ${cat.key}. Check console.`);
          return;
        }
        
        if (!r.ok) {
          console.error(`API error for ${cat.key}: ${r.status} ${r.statusText}`);
          updateStatusDisplay(`API error for ${cat.key}: ${r.status} ${r.statusText}`);
          return;
        }
        
        const offerData = await handleFetchResponse(r, `fetching global offers for ${cat.key}`, st);
        all.push(...(offerData.results || []));
      } catch (error) {
        if (error.message && error.message.startsWith('HTTP_429')) {
          updateStatusDisplay(`Data fetch for ${cat.key} hit 429. Pausing for 1 min.`);
          st.categoryDataFetchPausedUntil = Date.now() + 1 * 60 * 1000;
          return;
        }
        console.error(`Error fetching global offers for ${cat.key}. Details:`, error);
        return;
      }
    }

    // 2) map/filter
    const entries = all
      .filter(e=>{
        const offerObj = e.offer || e;
        const realmValue = offerObj.tradeEnvironmentValues[1].value;
        const factionValue = cat.needsFaction ? offerObj.tradeEnvironmentValues[2].value : null;
        
        // Specifically exclude Wild Growth Horde in SOD category
        if (cat.key === 'SOD' && realmValue === 'Wild Growth' && factionValue === 'Horde') {
          return false;
        }
        
        // If allowedCombinations is specified, use it to filter
        if (cat.allowedCombinations && cat.needsFaction) {
          return cat.allowedCombinations.includes(`${realmValue}:${factionValue}`);
        }
        
        return !cat.allowedRealms || cat.allowedRealms.includes(realmValue);
      })
      .map(e=>{
        const o=e.offer||e, t=o.tradeEnvironmentValues;
        return {
          realm: t[1].value,
          faction: cat.needsFaction? t[2].value : null,
          entryId: t[2]?.id || t[1].id,
          global: o.pricePerUnit.amount,
          you: getYourPrice(cat.key, t[2]?.id || t[1].id) // Use cached price
        };
      });

    // 3) Only fetch "you" prices if they haven't been loaded or need refreshing
    const entriesToFetch = entries.filter(en => shouldRefreshPrice(cat.key, en.entryId));
    
    if (entriesToFetch.length > 0) {
      logInfo(`Fetching ${entriesToFetch.length} new/outdated "You" prices for ${cat.key} (sequential with dashboard delay)`);
      
      // Fetch sequentially with dashboard delay (no concurrent batches)
      for (let i = 0; i < entriesToFetch.length; i++) {
        const en = entriesToFetch[i];
        
          try {
            const u = new URL('/api/predefinedOffers/me/', API_BASE);
            u.searchParams.set('gameId', cat.params.gameId);
            u.searchParams.set('category', 'Currency');
            u.searchParams.set('pageSize', '40');
            u.searchParams.set('pageIndex', '1');
            u.searchParams.set('tradeEnvironmentId', en.entryId);
            const r = await fetch(u, {credentials: 'include'});
            
            if (r.status === 429) {
              logWarning(`Rate limited fetching You price for ${en.realm}:${en.faction || ''}. Will try again later.`);
            continue;
            }
            
            if (r.ok) {
              const d = await r.json();
              const ps = (d.results || []).map(x => x.pricePerUnit.amount).filter(n => typeof n === 'number');
              const yourPrice = ps.length ? Math.min(...ps) : null;
              
              // Update cache and the entry
              setYourPrice(cat.key, en.entryId, yourPrice);
              en.you = yourPrice;
            
            // Immediately update UI if row exists to show fresh status
            const st = state.get(cat.key);
            if (st) {
              const key = `${cat.key}:${en.entryId}`;
              const row = st.rows.get(key);
              if (row && row.youCell) {
                row.youCell.textContent = yourPrice != null ? yourPrice.toFixed(5) : '—';
                row.youCell.className = 'you-price-fresh'; // Always fresh after successful fetch
              }
            }
            }
          } catch (error) {
            // Just log error, don't change en.you
            console.warn(`Error fetching You price for ${en.realm}:${en.faction || ''}:`, error);
          }
        
        // Apply dashboard delay between each request (except for the last one)
        if (i < entriesToFetch.length - 1) {
          const delaySeconds = randDelay();
          logInfo(`"You" price fetch delay: ${delaySeconds}s`);
          await new Promise(r => setTimeout(r, delaySeconds * 1000));
        }
      }
    }
    
    // Now update "you" field for all entries from cache
    for (const entry of entries) {
      if (entry.you === null) {
        entry.you = getYourPrice(cat.key, entry.entryId);
      }
    }

    const dollar = (sheetData.find(i=>i.realm==='Dollar')||{}).price||1;

    st.underList=[];

    // 4) render + enforce inactive & too‐high‐profit flags
    if (cat.key === 'Retail') {
      // For Retail, we need to distribute entries across the 4 sections
      const sectionCount = st.sections.length;
      const entriesPerSection = Math.ceil(entries.length / sectionCount);
      
      // Keep track of existing realms for smooth updates
      const existingRealms = new Map();
      st.rows.forEach((row, key) => {
        existingRealms.set(key, row);
      });
      
      // Clear existing rows in each section for complete redraw
      // This gives a cleaner UI when realm order changes from the API
      st.sections.forEach(section => {
        section.innerHTML = '';
      });
      
      // Create a new rows map to replace the old one
      const newRows = new Map();
      
      // Distribute entries across sections
      entries.forEach((en_api_data, index) => {
        const sectionIndex = Math.min(Math.floor(index / entriesPerSection), sectionCount - 1);
        const section = st.sections[sectionIndex];
        
        const key = `${cat.key}:${en_api_data.entryId}`;
        let row = existingRealms.get(key);
        
        // Important: For comparison logic, always use the cached global price
        // This prevents the issue where our price change affects the global price in the comparison
        const realmFactionKey = `${en_api_data.realm}:${en_api_data.faction || ''}`;
        const cachedGlobalPrice = st.globalPrices.get(realmFactionKey) || en_api_data.global;
        
        // Create or update row
        if (!row) {
          // Brand new realm
          const tr = document.createElement('tr');
          // Determine "You" price display with freshness indicator
          const youPriceText = en_api_data.you ? en_api_data.you.toFixed(5) : '—';
          const youPriceClass = en_api_data.you && !shouldRefreshPrice(cat.key, en_api_data.entryId) ? 'you-price-fresh' : 'you-price-stale';
          
          tr.innerHTML = `
            <td class="skip-cell"><input type="checkbox" class="skip"/></td>
            <td>${en_api_data.realm}</td>
            <td>${en_api_data.faction}</td>
            <td style="text-align:right" class="global-price-clickable">${cachedGlobalPrice.toFixed(5)}</td>
            <td style="text-align:right" class="${youPriceClass} you-price-clickable">${youPriceText}</td>
          `;
          section.appendChild(tr);
          const skipBox = tr.querySelector('.skip');
          const globalCell = tr.children[3];
          const youCell = tr.children[4];
          
          row = {
            tr,
            youCell: youCell, // Updated for faction column
            globalCell: globalCell, // Store reference to global cell
            skipBox,
            entryRef: en_api_data,
            consecutiveFailures: 0,
            postponedUntil: 0,
            realmKey: `${en_api_data.realm}:${en_api_data.faction || ''}`,
            hasAttemptedInactiveAdjustment: false,
            hasAttemptedBelowMinAdjustment: false,
            section: sectionIndex
          };
          
          skipBox.addEventListener('change', () => {
            // Reset manual price flag when realm is re-checked to allow auto processing again
            if (skipBox.checked && row.manuallyPriced) {
              row.manuallyPriced = false;
              row.manualPriceTimestamp = 0;
              row.tr.classList.remove('muted-manual-price');
              logInfo(`${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''} - Manual price protection removed, auto can process again`);
            }
            filterUnderList(cat);
          });
          
          // Add click handler to global price cell for copying to clipboard
          globalCell.addEventListener('click', () => {
            navigator.clipboard.writeText(cachedGlobalPrice.toFixed(5));
          });
          
          // Add click handler to "You" price cell for copying to clipboard
          youCell.addEventListener('click', () => {
            if (en_api_data.you != null) {
              navigator.clipboard.writeText(en_api_data.you.toFixed(5));
            }
          });
          
          // Apply the select all state if applicable
          if (st.selectAllRetail && st.selectAllRetail.checked) {
            skipBox.checked = true;
          }
        } else {
          // Existing realm that might need to move sections
          if (row.section !== sectionIndex || row.tr.parentNode !== section) {
            if (row.tr.parentNode) {
              row.tr.parentNode.removeChild(row.tr);
            }
            section.appendChild(row.tr);
            row.section = sectionIndex;
          }
          
          // Update data
          row.entryRef = en_api_data;
          row.tr.children[1].textContent = en_api_data.realm;
          row.tr.children[2].textContent = en_api_data.faction;
          if (!row.globalCell) row.globalCell = row.tr.children[3]; // Ensure we have the reference
          row.globalCell.textContent = cachedGlobalPrice.toFixed(5);
          row.globalCell.className = 'global-price-clickable'; // Ensure clickable class is applied
          row.youCell.textContent = en_api_data.you != null ? en_api_data.you.toFixed(5) : '—';
          row.youCell.className = (en_api_data.you && !shouldRefreshPrice(cat.key, en_api_data.entryId) ? 'you-price-fresh' : 'you-price-stale') + ' you-price-clickable';
          
          // Ensure click handlers exist (remove existing first to avoid duplicates)
          row.globalCell.replaceWith(row.globalCell.cloneNode(true));
          row.globalCell = row.tr.children[3]; // Update reference after clone
          row.globalCell.addEventListener('click', () => {
            navigator.clipboard.writeText(cachedGlobalPrice.toFixed(5));
          });
          
          row.youCell.replaceWith(row.youCell.cloneNode(true));
          row.youCell = row.tr.children[4]; // Update reference after clone
          row.youCell.addEventListener('click', () => {
            if (en_api_data.you != null) {
              navigator.clipboard.writeText(en_api_data.you.toFixed(5));
            }
          });
        }
        
        // Visual flags based on current data (same logic as non-Retail categories)
        row.skipBox.disabled = false;
        row.tr.classList.remove('muted','muted-too-many-failures','muted-below-min','muted-manual-price','inactive-flag','below-threshold','fallback-price','unchecked'); 
        
        // Check for manually priced realms first
        if (row.manuallyPriced) {
          row.tr.classList.add('muted-manual-price');
          console.log(`${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''}: PURPLE (manually priced)`);
        }
        // Check for too many consecutive failures
        else if (row.consecutiveFailures >= 3) {
          row.tr.classList.add('muted-too-many-failures');
          console.log(`${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''}: LIGHT RED (${row.consecutiveFailures} failures)`);
        }
        // Check for below minimum price (Retail)
        else if (cachedGlobalPrice < minRetail) {
          row.tr.classList.add('muted-below-min');
          console.log(`${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''}: LIGHT ORANGE (below min: ${cachedGlobalPrice} < ${minRetail})`);
        } else {
          console.log(`${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''}: NORMAL (no muted state)`);
        }
        
        // IMPORTANT: For Retail, always use the cached global price for comparison
        // This fixes the issue where our own price change makes global price appear to change
        const needsUndercutting = en_api_data.you != null && en_api_data.you > cachedGlobalPrice;
        row.tr.classList.toggle('highlight', needsUndercutting);
        if (needsUndercutting && row.skipBox.checked && !row.manuallyPriced) {
          // Exclude manually priced realms from auto processing
          if (!st.underList.includes(en_api_data)) st.underList.push(en_api_data);
        } else {
          // Remove from underList if not meeting undercut criteria or not checked or manually priced
          const underListIndex = st.underList.indexOf(en_api_data);
          if (underListIndex > -1) st.underList.splice(underListIndex, 1);
        }
        
        // Add to new rows map
        newRows.set(key, row);
      });
      
      // Replace the old rows map with the new one
      st.rows = newRows;
    } else {
      // Original code for non-Retail categories
      entries.forEach(en_api_data => {
      const key = `${cat.key}:${en_api_data.entryId}`;
      let row = st.rows.get(key);

      // Get fresh data for display and decision making from en_api_data
      const realmFactionKey = `${en_api_data.realm}:${en_api_data.faction}`;
      // Make lookup case-insensitive by converting both sides to lowercase
      const mapKey = Object.keys(ACTIVE_MAP).find(k => k.toLowerCase() === realmFactionKey.toLowerCase());
      const mappedRealm = mapKey ? ACTIVE_MAP[mapKey] : null;
      
      // Find active sheet entry, with better error handling
        const activeSheetEntry = (cat.key==='Anniversary' || cat.key==='SOD' || cat.key==='Cata') && mappedRealm ? 
        sheetData.find(i => i.realm && i.realm.toLowerCase() === mappedRealm.toLowerCase()) : null;
      
      console.log(`Lookup for ${realmFactionKey} -> ${mappedRealm} -> ${activeSheetEntry ? 'Found' : 'Not found'}`);
      
      const isMarkedInactive = (cat.key==='Anniversary' || cat.key==='SOD') && activeSheetEntry && !activeSheetEntry.active;
      const originalGlobalForCalc = en_api_data.global;
      
      // Store last known min prices per realm in localStorage if we get them successfully
      if (activeSheetEntry && activeSheetEntry.price && mappedRealm) {
        localStorage.setItem(`lastKnownMinPrice_${mappedRealm}`, activeSheetEntry.price);
      }
      
      // Get min price - first try sheet, then fallback to stored value
      let minPrice = 0;
      if (activeSheetEntry && activeSheetEntry.price) {
        minPrice = activeSheetEntry.price;
      } else if (mappedRealm && localStorage.getItem(`lastKnownMinPrice_${mappedRealm}`)) {
        minPrice = +localStorage.getItem(`lastKnownMinPrice_${mappedRealm}`);
        // Using fallback price silently - will be shown with light green background
      }
      
        // For Cata, check if there's additional profit margin in the sheet data
        let profitMargin = 0;
        if (cat.key === 'Cata' && mappedRealm) {
          // For Cata, the data is stored in the notes arrays of other objects
          // We need to search through all objects and their notes arrays
          let foundCataData = false;
          
          for (const entry of sheetData) {
            if (!entry.notes || !Array.isArray(entry.notes)) continue;
            
            // Look for the Cata realm name in the notes array
            const realmIndex = entry.notes.findIndex(note => {
              if (typeof note !== 'string') return false;
              // Case insensitive comparison and trim whitespace
              const normalizedNote = note.trim().toLowerCase();
              const normalizedRealm = mappedRealm.toLowerCase();
              return normalizedNote === normalizedRealm;
            });
            
            if (realmIndex >= 0 && realmIndex + 2 < entry.notes.length) {
              // Found the realm, next two values should be base price and profit
              console.log(`Found ${mappedRealm} in sheet data at index ${realmIndex} in entry: ${entry.realm}`);
              console.log(`Data: ${entry.notes[realmIndex]}, ${entry.notes[realmIndex+1]}, ${entry.notes[realmIndex+2]}`);
              
              const basePrice = parseFloat(entry.notes[realmIndex + 1]) * 1000;
              const addProfit = parseFloat(entry.notes[realmIndex + 2]) * 1000;
              
              if (!isNaN(basePrice) && !isNaN(addProfit)) {
                minPrice = basePrice + addProfit;
                console.log(`Found min price for ${mappedRealm}: ${minPrice} (base: ${basePrice}, profit: ${addProfit})`);
                foundCataData = true;
                break;
              }
            }
          }
          
          if (!foundCataData) {
            console.warn(`No sheet entry found for ${mappedRealm} in Cata category or invalid format`);
            // Dump the sheet data for debugging
            console.log('Sheet data entries with notes:');
            sheetData.filter(entry => entry.notes && Array.isArray(entry.notes)).forEach(entry => {
              console.log(`Entry ${entry.realm} notes:`, entry.notes);
            });
          }
        }
        
        // Get individual realm profit percentage
        const realmKey = `${en_api_data.realm}:${en_api_data.faction || ''}`;
        const realmPct = (cat.key==='Anniversary' || cat.key==='SOD' || cat.key==='Cata')
          ? (+localStorage.getItem(`profitPct_${cat.key}_${realmKey}`) || 5) / 100
          : 0;
        
        const sheetMinSellPrice = (cat.key==='Anniversary' || cat.key==='SOD' || cat.key==='Cata') && minPrice
        ? minPrice * (1 + realmPct) 
        : 0;
      
      const currentLocalValue = originalGlobalForCalc * 0.92 * dollar;
        const isBelowMinSell = (cat.key === 'Anniversary' || cat.key === 'SOD' || cat.key === 'Cata') && minPrice && currentLocalValue < sheetMinSellPrice;

      // We will attach fresh api data to row.entryRef later.
      // For status flags needed by action functions, they should be on en_api_data or passed directly.
      // Let's ensure en_api_data has these flags for the current cycle.
      en_api_data.isMarkedInactive = isMarkedInactive;
      en_api_data.isBelowMinSell = isBelowMinSell;
      // TargetPriceUSD calculation is based on current data, so it's fine on en_api_data for this cycle.
        if (minPrice && dollar && (cat.key === 'Anniversary' || cat.key === 'SOD' || cat.key === 'Cata')) {
        const sheetMinPriceLocal = minPrice;
        const eldoradoCutFactor = 0.92;
        if (isMarkedInactive) {
          en_api_data.targetPriceUSD = Math.round(((sheetMinPriceLocal * 1.4) / (eldoradoCutFactor * dollar)) * 100000) / 100000;
        } else if (isBelowMinSell) { // Use 'else if' to ensure only one target is set if both conditions were hypothetically true
          en_api_data.targetPriceUSD = Math.round(((sheetMinPriceLocal * 1.2) / (eldoradoCutFactor * dollar)) * 100000) / 100000;
        }
      }

      const displayGlobal = isMarkedInactive ? originalGlobalForCalc * 1.3 : originalGlobalForCalc;
      let minSheetPrice = null;
        if ((cat.key === 'Anniversary' || cat.key === 'SOD' || cat.key === 'Cata')) {
        minSheetPrice = minPrice || null;
      }

      if(!row){
        const tr=document.createElement('tr');
        let localPriceContent = (displayGlobal*0.92*dollar).toFixed(1);
        if ((cat.key === 'Anniversary' || cat.key === 'SOD') && minSheetPrice !== null) {
          localPriceContent += ` / ${minSheetPrice.toFixed(0)}`;
        }
          // Special formatting for Cata realms to clearly show min price
          else if (cat.key === 'Cata' && minSheetPrice !== null) {
            const convertedPrice = Math.round(displayGlobal * 0.92 * dollar);
            localPriceContent = `${convertedPrice} / ${minSheetPrice.toFixed(0)}`;
        }

        // Get stored realm-specific profit percentage
        const realmKey = `${en_api_data.realm}:${en_api_data.faction || ''}`;
        const storedProfitPct = localStorage.getItem(`profitPct_${cat.key}_${realmKey}`) || '5';
        
        // Determine "You" price display with freshness indicator for non-Retail
        const youPriceText = en_api_data.you ? en_api_data.you.toFixed(5) : '—';
        const youPriceClass = en_api_data.you && !shouldRefreshPrice(cat.key, en_api_data.entryId) ? 'you-price-fresh' : 'you-price-stale';
        
        tr.innerHTML=`
          <td class="skip-cell"><input type="checkbox" class="skip"/></td>
          <td>${en_api_data.realm}</td>
          ${cat.needsFaction?`<td>${en_api_data.faction}</td>`:''}
          <td style="text-align:right" class="global-price-clickable">${displayGlobal.toFixed(5)}</td>
          <td style="text-align:right" class="${youPriceClass} you-price-clickable">${youPriceText}</td>
          <td class="local" style="text-align:right">${localPriceContent}</td>
          <td style="text-align:right">
            <input type="number" class="profit-pct-input" step="1" style="width:50px;font-size:11px" value="${storedProfitPct}"/>
          </td>
          <td style="text-align:right">
            <input type="number" class="manual-price-input" step="1000" placeholder="Toman" style="width:70px;font-size:11px"/>
            <button class="manual-price-btn" style="padding:1px 4px;font-size:11px;margin-left:2px">✓</button>
          </td>
        `;
        st.tbody.append(tr);
        const skipBox = tr.querySelector('.skip');
        const globalCell = tr.children[cat.needsFaction?3:2];
        const youCell = tr.children[cat.needsFaction?4:3];

        const profitPctInput = tr.querySelector('.profit-pct-input');
        const manualPriceInput = tr.querySelector('.manual-price-input');
        const manualPriceBtn = tr.querySelector('.manual-price-btn');
        
        // Add helpful tooltips for manual pricing
        manualPriceInput.title = "Manual pricing always works - no restrictions";
        manualPriceBtn.title = "Set manual price - bypasses all auto restrictions";

        row = {
            tr,
            youCell: youCell,
            globalCell: globalCell,
            skipBox,
            profitPctInput,
            manualPriceInput,
            manualPriceBtn,
            entryRef: en_api_data, // Store current API data here
            // Persisted state for this realm/row:
            consecutiveFailures: 0,
            postponedUntil: 0,
              realmKey: `${en_api_data.realm}:${en_api_data.faction || ''}`, // Store for convenience
              hasAttemptedInactiveAdjustment: false,
              hasAttemptedBelowMinAdjustment: false
        };
        
        // Add click handler to global price cell for copying to clipboard
        globalCell.addEventListener('click', () => {
          navigator.clipboard.writeText(displayGlobal.toFixed(5));
        });
        
        // Add click handler to "You" price cell for copying to clipboard
        youCell.addEventListener('click', () => {
          if (en_api_data.you != null) {
            navigator.clipboard.writeText(en_api_data.you.toFixed(5));
          }
        });
          
        st.rows.set(key,row);
        skipBox.addEventListener('change',()=>{
          // Reset manual price flag when realm is re-checked to allow auto processing again
          if (skipBox.checked && row.manuallyPriced) {
            row.manuallyPriced = false;
            row.manualPriceTimestamp = 0;
            row.tr.classList.remove('muted-manual-price');
            logInfo(`${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''} - Manual price protection removed, auto can process again`);
          }
          filterUnderList(cat);
        }); // Add listener only once

        // Add event listener for profit percentage changes
        profitPctInput.addEventListener('change', () => {
          const realmKey = `${en_api_data.realm}:${en_api_data.faction || ''}`;
          const value = Math.max(0, parseInt(profitPctInput.value) || 0);
          profitPctInput.value = value;
          localStorage.setItem(`profitPct_${cat.key}_${realmKey}`, value.toString());
        });

        // Add event listener for manual price setting
        manualPriceBtn.addEventListener('click', async () => {
          const tomanPrice = parseFloat(manualPriceInput.value);
          if (isNaN(tomanPrice) || tomanPrice <= 0) {
            alert('Please enter a valid price in Toman');
            return;
          }

          // MANUAL MODE: Always allow regardless of muted state
          // Convert Toman to USD using current exchange rate
          const dollar = (sheetData.find(i=>i.realm==='Dollar')||{}).price || 1;
          const usdPrice = tomanPrice / (0.92 * dollar); // Reverse the conversion from Local column
          
          manualPriceBtn.disabled = true;
          manualPriceBtn.textContent = '...';
          
          if (await setManualPrice(cat, en_api_data, row, usdPrice)) {
            logSuccess(`Manual price set for ${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''} = ${usdPrice.toFixed(5)} USD (${tomanPrice} Toman)`);
            
            // Mark as manually priced to prevent auto processing
            row.manuallyPriced = true;
            row.manualPriceTimestamp = Date.now();
            
            // Uncheck the realm so auto won't override the manual price
            skipBox.checked = false;
            tr.classList.remove('muted', 'muted-too-many-failures', 'muted-below-min');
            tr.classList.add('muted-manual-price');
            filterUnderList(cat);
            manualPriceInput.value = ''; // Clear the input
            
            // Verify price after 5 seconds (only for this specific realm)
            setTimeout(() => {
              refreshSpecificRealmPrice(cat, en_api_data.entryId, `${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''}`);
            }, 5000);
          }
          
          manualPriceBtn.disabled = false;
          manualPriceBtn.textContent = '✓';
        });
      } else {
        // Row exists, update its entryRef with fresh API data
        row.entryRef = en_api_data;
        // DO NOT reset row.consecutiveFailures or row.postponedUntil here
        
        // Update profit percentage input with stored value if it exists
        if (row.profitPctInput) {
          const currentStoredPct = localStorage.getItem(`profitPct_${cat.key}_${realmKey}`) || '5';
          if (row.profitPctInput.value !== currentStoredPct) {
            row.profitPctInput.value = currentStoredPct;
          }
        }
      }

      // Update displayed data in existing TR cells using en_api_data
      const globalCellIndex = cat.needsFaction?3:2;
      const globalCell = row.tr.children[globalCellIndex];
      globalCell.textContent = displayGlobal.toFixed(5);
      globalCell.className = 'global-price-clickable'; // Ensure clickable class is applied
      
      // Ensure click handler exists (remove existing first to avoid duplicates)
      if (!row.globalCell) {
        row.globalCell = globalCell;
        globalCell.addEventListener('click', () => {
          navigator.clipboard.writeText(displayGlobal.toFixed(5));
        });
      }
      
      // Update "You" cell with freshness indicator
      if (en_api_data.you != null) {
        row.youCell.textContent = en_api_data.you.toFixed(5);
        row.youCell.className = (shouldRefreshPrice(cat.key, en_api_data.entryId) ? 'you-price-stale' : 'you-price-fresh') + ' you-price-clickable';
      } else {
        row.youCell.textContent = '—';
        row.youCell.className = 'you-price-stale you-price-clickable';
      }
      
      // Ensure "You" price click handler exists for existing rows
      if (!row.youCellClickHandlerAdded) {
        row.youCell.addEventListener('click', () => {
          if (en_api_data.you != null) {
            navigator.clipboard.writeText(en_api_data.you.toFixed(5));
          }
        });
        row.youCellClickHandlerAdded = true;
      }
      
      // Update Local column for non-Retail categories
        const localCellIndex = cat.needsFaction ? 5 : 4;
        if (row.tr.children[localCellIndex]) {
          let localPriceContent = (displayGlobal*0.92*dollar).toFixed(1); 
          if ((cat.key === 'Anniversary' || cat.key === 'SOD') && minSheetPrice !== null) {
            localPriceContent += ` / ${minSheetPrice.toFixed(0)}`;
          }
            // Special formatting for Cata realms to clearly show min price
            else if (cat.key === 'Cata' && minSheetPrice !== null) {
              const convertedPrice = Math.round(displayGlobal * 0.92 * dollar);
              localPriceContent = `${convertedPrice} / ${minSheetPrice.toFixed(0)}`;
          }
          row.tr.children[localCellIndex].textContent = localPriceContent;
      }

      // Visual flags based on current data (en_api_data)
      row.skipBox.disabled=false; // Always ensure skipbox is enabled now
      row.tr.classList.remove('muted','muted-too-many-failures','muted-below-min','muted-manual-price','inactive-flag','below-threshold','fallback-price','unchecked'); 
      
      // Check for manually priced realms first
      if (row.manuallyPriced) {
        row.tr.classList.add('muted-manual-price');
        console.log(`${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''}: PURPLE (manually priced)`);
      }
      // Check for too many consecutive failures
      else if (row.consecutiveFailures >= 3) {
        row.tr.classList.add('muted-too-many-failures');
        console.log(`${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''}: LIGHT RED (${row.consecutiveFailures} failures)`);
      }
      // Check for below minimum price
      else if(isBelowMinSell){
        row.tr.classList.add('muted-below-min');
        console.log(`${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''}: LIGHT ORANGE (below min sell)`);
      }
      // Check for fallback price usage (light green background)
      else if (!activeSheetEntry && mappedRealm && localStorage.getItem(`lastKnownMinPrice_${mappedRealm}`)) {
        row.tr.classList.add('fallback-price');
        console.log(`${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''}: LIGHT GREEN (using fallback price)`);
      } else {
        console.log(`${en_api_data.realm}${en_api_data.faction ? ` ${en_api_data.faction}` : ''}: NORMAL (no muted state)`);
      }
      
      // Additional flags for inactive/below threshold (these don't change background color)
      if(isMarkedInactive){
        row.tr.classList.add('inactive-flag');
      }
      if(isBelowMinSell){
        row.tr.classList.add('below-threshold');
      }

      const needsUndercutting = en_api_data.you != null && en_api_data.you > displayGlobal && !isMarkedInactive && !isBelowMinSell;
      row.tr.classList.toggle('highlight', needsUndercutting ); 
      if(needsUndercutting && row.skipBox.checked && !row.manuallyPriced){
        // Add the fresh en_api_data to underList, not the whole row object
        // But exclude manually priced realms from auto processing
        if (!st.underList.includes(en_api_data)) st.underList.push(en_api_data);
      } else {
        // Remove from underList if not meeting undercut criteria or not checked or manually priced
        const underListIndex = st.underList.indexOf(en_api_data);
        if (underListIndex > -1) st.underList.splice(underListIndex, 1);
      }
    });
    }

    // The underList now ONLY contains items that need standard undercutting.

    // Only disable buttons if we're not in auto mode
    st.btnUnder.disabled = st.underList.length===0;
  }

  // ───────── CONTROL ───────────────────────────────────────
  function startMasterIntervalIfNeeded(){
    if(masterInterval) return; // Already running
    if(state.size === 0) return; // No active categories

    let intervalSeconds = parseInt(localStorage.getItem('elcut_interval') || '30', 10);
    if (isNaN(intervalSeconds) || intervalSeconds < 5) {
        intervalSeconds = 30; // Sensible default if stored value is invalid or too low
    }
    
    // updateStatusDisplay(`Master interval active (${intervalSeconds}s). Fetching data...`); // This might be too noisy
    masterRun(); // Run once immediately
    masterInterval = setInterval(masterRun, intervalSeconds * 1000);
    console.log(`Master interval started (${intervalSeconds}s).`);
  }

  function stopMasterIntervalIfNoCategoriesActive(){
    if(!masterInterval) return; // Not running
    if(state.size > 0) return; // Still active categories

    clearInterval(masterInterval);
    masterInterval = null;
    console.log('Master interval stopped.');
  }

  function randDelay() {
    let min = parseFloat(UI.minDelay.value);
    let max = parseFloat(UI.maxDelay.value);

    if (isNaN(min) || min < 0.1) min = 0.1; // Allow as low as 0.1 seconds
    if (isNaN(max) || max < min) max = min;
    
    // Ensure max is not excessively large
    max = Math.min(max, 60);
    min = Math.min(min, max);

    // Use 2 decimal places for the calculation for better precision
    return +(Math.random()*(max-min)+min).toFixed(2);
  }

  // ───────── INIT ─────────────────────────────────────────
  createPanel();
  renderToggles();
  UI.showLog.addEventListener('click', ()=>{
    window.open(chrome.runtime.getURL('log.html'),'ElCutLog','width=400,height=500');
  });

  // ───────── MANUAL PRICE SETTING ─────────────────────────────────
  async function setManualPrice(cat, en, row, usdPrice) {
    // MANUAL MODE: NO RESTRICTIONS - user can always set manual prices
    
    try {
      const realmKey = `${en.realm}:${en.faction || ''}`;
      
      // Fetch current offers
      const u = new URL('/api/predefinedOffers/me/', API_BASE);
      u.searchParams.set('gameId', cat.params.gameId);
      u.searchParams.set('category', 'Currency');
      u.searchParams.set('pageSize', '40');
      u.searchParams.set('pageIndex', '1');
      u.searchParams.set('tradeEnvironmentId', en.entryId);
      
      let r;
      try {
        r = await fetch(u, {credentials: 'include'});
      } catch (networkError) {
        logError(`Network error fetching offers for ${realmKey}: ${networkError.message}`);
        return false;
      }
      
      if (r.status === 429) {
        logWarning(`Rate limited fetching offers for ${realmKey}. Try again later.`);
        return false;
      }
      
      if (!r.ok) {
        logError(`Failed to fetch offers for ${realmKey}: ${r.status}`);
        return false;
      }
      
      const offerData = await r.json();
      const offers = offerData.results || [];
      
      if (offers.length === 0) {
        logWarning(`No offers found for ${realmKey}`);
        return false;
      }
      
      // Get XSRF token
      const cookie = document.cookie.split('; ').find(c => c.startsWith('__Host-XSRF-TOKEN='));
      const token = cookie && decodeURIComponent(cookie.split('=')[1]);
      if (!token) {
        logError('XSRF token not found');
        return false;
      }
      
      // Update all offers with the new price
      const updatePromises = offers.map(offer =>
        fetch(`${API_BASE}/api/predefinedOffersUser/me/${offer.id}/changePrice/`, {
          method: 'PUT',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'X-XSRF-TOKEN': token
          },
          body: JSON.stringify({
            amount: usdPrice,
            currency: 'USD'
          })
        })
      );
      
      const results = await Promise.all(updatePromises.map(p => 
        p.catch(error => ({ ok: false, status: 0, error }))
      ));
      
      // Check for failures
      const failed = results.filter(res => !res.ok);
      if (failed.length > 0) {
        logError(`Failed to update ${failed.length} offers for ${realmKey}`);
        return false;
      }
      
      // Update cache
      setYourPrice(cat.key, en.entryId, usdPrice);
      if (row.entryRef) row.entryRef.you = usdPrice;
      
      return true;
    } catch (error) {
      logError(`Error setting manual price for ${en.realm}:${en.faction || ''}: ${error.message}`);
      return false;
    }
  }

  // ───────── MANUAL & AUTO FUNCTIONS ─────────────────────────────────
  async function manualUndercut(cat) {
    // Add safety check
    if (!canChangePrice()) {
      return;
    }
    
    const st = state.get(cat.key);
    for (const en of st.underList) {
      // We need to find the row that corresponds to this entry
      const entryKey = `${cat.key}:${en.entryId}`;
      const row = st.rows.get(entryKey);
      if (row) {
        await doUndercut(cat, en, row);
        break; // Only do one undercut for manual mode
      }
    }
    fetchAndCompare(cat);
  }

  function toggleAuto(cat, btn) {
    const st = state.get(cat.key);
    st.autoRunning = !st.autoRunning;
    btn.textContent = st.autoRunning ? 'Auto ❚❚' : 'Auto ▶';
    btn.style.color = st.autoRunning ? 'red' : 'green';

    if (!st.autoRunning) {
      // Stop auto mode
      if (st.autoLoopTimeoutId) clearTimeout(st.autoLoopTimeoutId);
      if (st.currentDelayTimeout) clearTimeout(st.currentDelayTimeout);
      if (st.competitorMonitoringInterval) {
        clearInterval(st.competitorMonitoringInterval);
        st.competitorMonitoringInterval = null;
      }
      // Hide competitor UI when stopping auto
      if (cat.key === 'Retail') {
        hideCompetitorUI(cat);
      }
      return;
    }
    
    // Start auto mode loop
    (async function loop() {
        st.autoLoopTimeoutId = null; 
      st.currentDelayTimeout = null;
      
        try {
            if (!st.autoRunning) return;

        // Safety check before processing
        if (!canChangePrice()) {
          st.autoRunning = false;
          btn.textContent = 'Auto ▶';
          btn.style.color = 'green';
          return;
        }

                    await fetchAndCompare(cat);
            if (!st.autoRunning) return;

        let processedAnyRealms = false;

        // Special handling for Retail category
        if (cat.key === 'Retail') {
          // Check if any realm needs undercutting and if lowest competitor is below min
          const hasRealmsBelowMin = Array.from(st.rows.values()).some(row => 
            row.skipBox.checked && row.entryRef && row.entryRef.global < minRetail
          );
          
          if (hasRealmsBelowMin) {
            // Check competitors to see if we should show the UI
            const competitors = await fetchRetailCompetitors();
            const lowestCompetitorPrice = competitors.length > 0 ? competitors[0].price : Infinity;
            
            if (lowestCompetitorPrice < minRetail) {
              logWarning('Retail: Lowest competitor price is below minimum threshold - showing competitor selection UI');
              showCompetitorUI(cat);
              // Don't process any realms in this cycle, let the UI handle it
              if (st.autoRunning) {
                st.autoLoopTimeoutId = setTimeout(loop, 30000);
              }
              return;
            } else {
              // Can now do normal undercutting
              hideCompetitorUI(cat);
            }
          }
        }

        // Periodic verification of Bonyadi undercutting (every 10 cycles)
        if (dualAccountState.coordinationEnabled && cat.key === 'Retail') {
          st.verificationCycle = (st.verificationCycle || 0) + 1;
          if (st.verificationCycle >= 10) {
            st.verificationCycle = 0;

            // Verify a few random realms
            const activeRealms = Array.from(st.rows.values())
              .filter(row => row.skipBox.checked && row.entryRef)
              .slice(0, 3); // Check first 3 active realms

            for (const row of activeRealms) {
              setTimeout(() => {
                verifyBonyadiUndercutting(row.entryRef.realm, row.entryRef.faction);
              }, Math.random() * 5000); // Spread out over 5 seconds
            }
          }
        }

        // Create a queue of realms to process
        const realmQueue = Array.from(st.rows.values())
          .filter(row => row.skipBox.checked && st.autoRunning)
          .sort((a, b) => {
            // Prioritize realms that need undercutting
            const aUndercut = a.entryRef && a.entryRef.you != null && a.entryRef.you > a.entryRef.global &&
                              !a.entryRef.isMarkedInactive && !a.entryRef.isBelowMinSell;
            const bUndercut = b.entryRef && b.entryRef.you != null && b.entryRef.you > b.entryRef.global && 
                              !b.entryRef.isMarkedInactive && !b.entryRef.isBelowMinSell;
            
            if (aUndercut && !bUndercut) return -1;
            if (!aUndercut && bUndercut) return 1;
            return 0;
          });

        // Process one realm at a time with delays between each realm
        for (let realmIndex = 0; realmIndex < realmQueue.length; realmIndex++) {
          const row = realmQueue[realmIndex];
          if (!st.autoRunning) break;

          // Safety check before each realm
          if (!canChangePrice()) {
            st.autoRunning = false;
            btn.textContent = 'Auto ▶';
            btn.style.color = 'green';
            return;
          }

          const en = row.entryRef;
                if (!en) continue; 

          // Skip postponed realms
                if (row.postponedUntil && Date.now() < row.postponedUntil) {
                  continue; 
                }
          
                if (row.postponedUntil && Date.now() >= row.postponedUntil) {
                    row.postponedUntil = 0;
                    row.consecutiveFailures = 0; 
            row.tr.style.color = "";
                }

          let actionTaken = false;

          // Decisions based on fresh data in 'en'
                if (en.isMarkedInactive) {
                    if (en.targetPriceUSD !== undefined && Math.abs((en.you || 0) - en.targetPriceUSD) < UND_STEP) {
              continue;
                    } else if (en.targetPriceUSD !== undefined) {
              if (row.hasAttemptedInactiveAdjustment) {
                continue;
                    } else {
                updateStatusDisplay(`Auto ${cat.key}: Adjusting ${row.realmKey} (Inactive) to ${en.targetPriceUSD.toFixed(5)}...`);
                const success = await adjustPriceRelatively(cat, en, 1.4, row);
                row.hasAttemptedInactiveAdjustment = true;
                processedAnyRealms = true;
                actionTaken = success;
              }
                    }
                } else if (en.isBelowMinSell) {
                    if (en.targetPriceUSD !== undefined && Math.abs((en.you || 0) - en.targetPriceUSD) < UND_STEP) {
              continue;
                    } else if (en.targetPriceUSD !== undefined) {
              if (row.hasAttemptedBelowMinAdjustment) {
                continue;
                    } else {
                updateStatusDisplay(`Auto ${cat.key}: Adjusting ${row.realmKey} (Below Min) to ${en.targetPriceUSD.toFixed(5)}...`);
                const success = await adjustPriceRelatively(cat, en, 1.2, row);
                row.hasAttemptedBelowMinAdjustment = true;
                processedAnyRealms = true;
                actionTaken = success;
              }
                    }
                } else if (en.you != null && en.you > en.global) { 
                    updateStatusDisplay(`Auto ${cat.key}: Undercutting ${row.realmKey}...`);
            const success = await doUndercut(cat, en, row);
            processedAnyRealms = true;
            actionTaken = success;
          }
          
          // Apply delay between each realm processing
          if (st.autoRunning && realmIndex < realmQueue.length - 1) {
            const delaySeconds = randDelay();
            logInfo(`Realm processing delay: ${delaySeconds}s before next realm...`);
            
            if (st.currentDelayTimeout) clearTimeout(st.currentDelayTimeout);
            
            await new Promise(resolve => {
              st.currentDelayTimeout = setTimeout(() => {
                st.currentDelayTimeout = null;
                resolve();
              }, delaySeconds * 1000);
            });
            
            if (!st.autoRunning) break;
          }
        }
        
        // Reset attempt flags at the end of the cycle
        st.rows.forEach(row => {
          row.hasAttemptedInactiveAdjustment = false;
          row.hasAttemptedBelowMinAdjustment = false;
        });
            
            if (!st.autoRunning) {
                if(st.autoLoopTimeoutId) clearTimeout(st.autoLoopTimeoutId);
                return;
            }
            
        let delay = processedAnyRealms ? 5000 : 10000;
                    updateStatusDisplay(`Auto ${cat.key}: Monitoring...`);

            st.autoLoopTimeoutId = setTimeout(loop, delay);

        } catch (error) {
            if (error?.message?.includes('Extension context invalidated')) {
            st.autoRunning = false;
                btn.textContent = 'Auto ▶';
          btn.style.color = 'green';
                if (st.autoLoopTimeoutId) clearTimeout(st.autoLoopTimeoutId);
          if (st.currentDelayTimeout) clearTimeout(st.currentDelayTimeout);
                return;
            }
            console.error('Auto-undercut loop error:', error);
        logError(`Auto-undercut loop error: ${error.message}`);
        st.autoLoopTimeoutId = setTimeout(loop, 5000);
        }
    })();
  }

  async function doUndercut(cat, en, row) {
    // Add safety check
    if (!canChangePrice()) {
      return false;
    }

    const st = state.get(cat.key);
    const realmKey = row.realmKey;

    // Check if we should switch accounts for this realm
    if (dualAccountState.coordinationEnabled && shouldSwitchAccount(en.realm, en.faction)) {
      const newAccount = switchAccount(en.realm, en.faction);

      if (newAccount === SECONDARY_ACCOUNT) {
        // Request secondary account to handle this price change
        const targetPrice = Math.round((en.global - UND_STEP) * 100000) / 100000;
        const adjustedPrice = cat.key === 'Retail' ? Math.max(targetPrice, minRetail) : targetPrice;

        const success = await requestSecondaryAccountPriceChange(en.realm, en.faction, adjustedPrice);
        if (success) {
          logSuccess(`Price change delegated to ${SECONDARY_ACCOUNT} for ${realmKey}`);
          return true; // Consider it successful since we delegated it
        } else {
          logWarning(`Failed to delegate to ${SECONDARY_ACCOUNT}, falling back to ${MAIN_ACCOUNT}`);
          dualAccountState.currentAccount = MAIN_ACCOUNT; // Fall back to main account
        }
      }
    }

    try {
        let offs = [];
        try {
        const u = new URL('/api/predefinedOffers/me/', API_BASE);
        u.searchParams.set('gameId', cat.params.gameId);
        u.searchParams.set('category', 'Currency');
        u.searchParams.set('pageSize', '40');
        u.searchParams.set('pageIndex', '1');
        u.searchParams.set('tradeEnvironmentId', en.entryId);
        const r = await fetch(u, {credentials: 'include'});
        
            if (r.status === 429) {
                updateStatusDisplay(`Rate limited when fetching offers for ${realmKey}. Postponing for 10 seconds.`);
                setTimeout(() => {
                    if (st.autoRunning) {
                        updateStatusDisplay(`Retrying ${realmKey} after rate limit cooldown...`);
                    }
                }, 10000);
                return false;
            }
            
            const offerData = await handleFetchResponse(r, `fetching offers for ${realmKey} (doUndercut)`, st);
            offs = offerData.results || [];
        } catch (error) {
            console.warn(`Failed to fetch own offers for ${realmKey} before undercutting:`, error.message);
            logWarning(`Failed to fetch own offers for ${realmKey} before undercutting: ${error.message}`);
            row.consecutiveFailures = (row.consecutiveFailures || 0) + 1;
            if (row.consecutiveFailures >= 2) {
                row.postponedUntil = Date.now() + 5 * 60 * 1000;
                updateStatusDisplay(`Realm ${realmKey} postponed for 5 min due to repeated failures.`);
                row.consecutiveFailures = 0; 
            }
            return false; 
        }

        const oldVal = en.you;
        let np = Math.round((en.global - UND_STEP) * 100000) / 100000;
      if (cat.key === 'Retail') np = Math.max(np, minRetail);

      const cookie = document.cookie.split('; ').find(c => c.startsWith('__Host-XSRF-TOKEN='));
      const token = cookie && decodeURIComponent(cookie.split('=')[1]);
      if (!token) return alert('Missing XSRF');

      const updatePromises = offs.map(o =>
        fetch(`${API_BASE}/api/predefinedOffersUser/me/${o.id}/changePrice/`, {
          method: 'PUT', credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'X-XSRF-TOKEN': token
          },
          body: JSON.stringify({ amount: np, currency: 'USD' })
        })
      );
      
        let hasFailed = false;
        const results = await Promise.all(updatePromises.map(p => 
            p.catch(error => {
                hasFailed = true;
                return { ok: false, status: 0, error };
            })
        ));
            
        const has429 = results.some(res => res.status === 429);
        if (has429) {
            updateStatusDisplay(`Rate limited changing price for ${realmKey}. Will retry in 10 seconds.`);
            row.consecutiveFailures = (row.consecutiveFailures || 0) + 1;

            // Report rate limit to dual account system
            if (dualAccountState.coordinationEnabled) {
                await reportRateLimit(dualAccountState.currentAccount, en.realm, en.faction);
            }

            setTimeout(() => {
                if (st.autoRunning) {
                    updateStatusDisplay(`Retrying ${realmKey} after rate limit cooldown...`);
                }
            }, 10000);

            if (row.consecutiveFailures >= 2) {
                row.postponedUntil = Date.now() + 5 * 60 * 1000;
                row.consecutiveFailures = 0;
                updateStatusDisplay(`Realm ${realmKey} postponed for 5 min due to rate limiting.`);
                row.tr.style.color = "#aaa";
            }
            return false;
        }
        
        if (hasFailed || results.some(res => !res.ok)) {
            updateStatusDisplay(`Failed to update price for ${realmKey}.`);
            row.consecutiveFailures = (row.consecutiveFailures || 0) + 1;
            if (row.consecutiveFailures >= 2) {
                row.postponedUntil = Date.now() + 5 * 60 * 1000;
                row.consecutiveFailures = 0;
                updateStatusDisplay(`Realm ${realmKey} postponed for 5 min due to errors.`);
                row.tr.style.color = "#aaa";
            }
            return false;
        }

      // Success
        row.consecutiveFailures = 0;
        row.postponedUntil = 0;
      row.tr.style.color = "";

        // Update cache and UI
        setYourPrice(cat.key, en.entryId, np);
      if (row.entryRef) row.entryRef.you = np;

        // Increment account change count for dual account coordination
        if (dualAccountState.coordinationEnabled) {
            incrementAccountChangeCount(en.realm, en.faction);
        }

        // Mark for refresh after 10 seconds to confirm the change
        setTimeout(() => {
            markPriceForRefresh(cat.key, en.entryId);
        }, 10000);

        logPriceChange(en.realm, en.faction, oldVal, np, cat.key, "Undercut");

        const d = new Date(),
              ts = `${d.getMonth()+1}/${d.getDate()}-${d.toLocaleTimeString()}`,
              rstr = `${en.realm}${en.faction?(' '+en.faction.charAt(0)):''}`,
              log  = `${ts} — ${rstr}: ${(oldVal ? oldVal.toFixed(5) : 'N/A')}→ ${np.toFixed(5)}`;
        chrome.storage.local.get('elcutLogs', res=>{
            const L = res.elcutLogs||[];
            L.push(log);
            chrome.storage.local.set({ elcutLogs:L });
        });
        return true; 
    } catch (error) {
        if (error?.message?.includes('Extension context invalidated')) throw error;
        
        row.consecutiveFailures = (row.consecutiveFailures || 0) + 1;
        if (row.consecutiveFailures >= 2) {
            row.postponedUntil = Date.now() + 5 * 60 * 1000;
            row.consecutiveFailures = 0; 
            updateStatusDisplay(`Realm ${realmKey} postponed for 5 min.`);
            row.tr.style.color = "#aaa";
        }
        return false;
    }
  }

  async function adjustPriceRelatively(cat, en, priceAdjustmentFactor, row) {
    // Add safety check
    if (!canChangePrice()) {
      return false;
    }
    
    const st = state.get(cat.key);
    const realmKey = row.realmKey;

    try {
      let offs = [];
      try {
        const u = new URL('/api/predefinedOffers/me/', API_BASE);
        u.searchParams.set('gameId', cat.params.gameId);
        u.searchParams.set('category', 'Currency');
        u.searchParams.set('pageSize', '40');
        u.searchParams.set('pageIndex', '1');
        u.searchParams.set('tradeEnvironmentId', en.entryId);
        const r = await fetch(u, { credentials: 'include' });
        
        if (r.status === 429) {
            updateStatusDisplay(`Rate limited when fetching offers for ${realmKey}. Postponing for 10 seconds.`);
            setTimeout(() => {
                if (st.autoRunning) {
                    updateStatusDisplay(`Retrying ${realmKey} after rate limit cooldown...`);
                }
            }, 10000);
            return false;
        }
        
        const offerData = await handleFetchResponse(r, `fetching offers for ${realmKey} (adjustPrice)`, st);
        offs = offerData.results || [];
      } catch (error) {
        row.consecutiveFailures = (row.consecutiveFailures || 0) + 1;
        if (row.consecutiveFailures >= 2) {
          row.postponedUntil = Date.now() + 5 * 60 * 1000;
          row.consecutiveFailures = 0; 
          updateStatusDisplay(`Realm ${realmKey} postponed for 5 min.`);
          row.tr.style.color = "#aaa";
        }
        return false; 
      }

      if (offs.length === 0) {
        return true; 
      }

      const oldVal = en.you; 
      let np = en.targetPriceUSD; 

      if (en.isMarkedInactive && priceAdjustmentFactor === 1.4 && np === undefined) {
        return false;
      }
      if (en.isBelowMinSell && !en.isMarkedInactive && priceAdjustmentFactor === 1.2 && np === undefined) {
        return false;
      }

      if (np === undefined || np === null) {
        return false;
      }

      if (cat.key === 'Retail') np = Math.max(np, minRetail);

      const cookie = document.cookie.split('; ').find(c => c.startsWith('__Host-XSRF-TOKEN='));
      const token = cookie && decodeURIComponent(cookie.split('=')[1]);
      if (!token) {
        return false;
      }

      const updatePromises = offs.map(o =>
        fetch(`${API_BASE}/api/predefinedOffersUser/me/${o.id}/changePrice/`, {
          method: 'PUT', credentials: 'include',
          headers: { 'Content-Type':'application/json', 'X-XSRF-TOKEN': token },
          body: JSON.stringify({ amount: np, currency: 'USD' })
        })
      );

      let hasFailed = false;
      const results = await Promise.all(updatePromises.map(p => 
          p.catch(error => {
              hasFailed = true;
              return { ok: false, status: 0, error };
          })
      ));
          
      const has429 = results.some(res => res.status === 429);
      if (has429) {
          updateStatusDisplay(`Rate limited changing price for ${realmKey}. Will retry in 10 seconds.`);
          row.consecutiveFailures = (row.consecutiveFailures || 0) + 1;
          
          setTimeout(() => {
              if (st.autoRunning) {
                  updateStatusDisplay(`Retrying ${realmKey} after rate limit cooldown...`);
              }
          }, 10000);
          
          if (row.consecutiveFailures >= 2) {
              row.postponedUntil = Date.now() + 5 * 60 * 1000;
              row.consecutiveFailures = 0;
              updateStatusDisplay(`Realm ${realmKey} postponed for 5 min due to rate limiting.`);
              row.tr.style.color = "#aaa";
          }
          return false;
      }
      
      if (hasFailed || results.some(res => !res.ok)) {
          updateStatusDisplay(`Failed to update price for ${realmKey}.`);
          row.consecutiveFailures = (row.consecutiveFailures || 0) + 1;
          if (row.consecutiveFailures >= 2) {
              row.postponedUntil = Date.now() + 5 * 60 * 1000;
              row.consecutiveFailures = 0;
              updateStatusDisplay(`Realm ${realmKey} postponed for 5 min due to errors.`);
              row.tr.style.color = "#aaa";
          }
          return false;
      }

      // Success
      row.consecutiveFailures = 0; 
      row.postponedUntil = 0; 
      row.tr.style.color = "";
      
      // Update cache and UI
      setYourPrice(cat.key, en.entryId, np);
      if (row.entryRef) row.entryRef.you = np;
      
      // Mark for refresh after 10 seconds to confirm the change
      setTimeout(() => {
          markPriceForRefresh(cat.key, en.entryId);
      }, 10000);

      let adjustmentReason = "Price Adjustment";
      if (en.isMarkedInactive) {
        adjustmentReason = "Inactive Adjustment";
      } else if (en.isBelowMinSell) {
        adjustmentReason = "Below Min Adjustment";
      }
      
      logPriceChange(en.realm, en.faction, oldVal, np, cat.key, adjustmentReason);

      const d = new Date(),
        ts = `${d.getFullYear()}-${String(d.getMonth()+1).padStart(2,'0')}-${String(d.getDate()).padStart(2,'0')} ${d.toLocaleTimeString()}`,
        rstr = `${en.realm}${en.faction?(' '+en.faction.charAt(0)):''}`,
        log = `${ts} — ADJUST ${rstr}: ${oldVal ? oldVal.toFixed(5) : 'N/A'} → ${np.toFixed(5)} (Context Factor: ${priceAdjustmentFactor})`;
      chrome.storage.local.get('elcutLogs', res => {
        const L = res.elcutLogs || [];
        L.push(log);
        chrome.storage.local.set({ elcutLogs: L });
      });

      return true;
    } catch (error) {
      if (error?.message?.includes('Extension context invalidated')) throw error;
      
      row.consecutiveFailures = (row.consecutiveFailures || 0) + 1;
      if (row.consecutiveFailures >= 2) {
        row.postponedUntil = Date.now() + 5 * 60 * 1000;
        row.consecutiveFailures = 0; 
        updateStatusDisplay(`Realm ${realmKey} postponed for 5 min.`);
        row.tr.style.color = "#aaa";
      }
      return false;
    }
  }

  // ───────── COMPETITOR ANALYSIS FOR RETAIL ─────────────────────────────────
  const KAZZAK_COMPETITORS_API = 'https://www.eldorado.gg/api/predefinedOffers/augmentedGame/bb744a6a-3c2d-44cc-9f51-117041e65f26?pageIndex=1&pageSize=20';
  
  async function fetchRetailCompetitors() {
    try {
      const response = await fetch(KAZZAK_COMPETITORS_API, {credentials: 'include'});
      if (!response.ok) {
        logError(`Failed to fetch competitors: ${response.status}`);
        return [];
      }
      
      const data = await response.json();
      const competitors = (data.results || []).slice(0, 3).map(result => ({
        username: result.user.username,
        price: result.offer.pricePerUnit.amount,
        stock: result.offer.quantity,
        userId: result.offer.userId
      }));
      
      logInfo(`Fetched ${competitors.length} top competitors`);
      return competitors;
    } catch (error) {
      logError(`Error fetching competitors: ${error.message}`);
      return [];
    }
  }

  function createCompetitorSelectionUI(cat) {
    const st = state.get(cat.key);
    if (!st) return;

    // Create overlay for the retail container
    let overlay = st.wrapper.querySelector('.competitor-overlay');
    if (!overlay) {
      overlay = document.createElement('div');
      overlay.className = 'competitor-overlay';
      overlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 6px;
        padding: 20px;
        z-index: 1000;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      `;
      
      st.wrapper.style.position = 'relative';
      st.wrapper.appendChild(overlay);
    }

    overlay.innerHTML = `
      <div style="text-align: center; max-width: 500px;">
        <h3 style="color: #d9534f; margin-bottom: 15px;">⚠️ Cannot Undercut Lowest Price</h3>
        <p style="margin-bottom: 20px;">The lowest competitor price is below our minimum threshold. Choose where to position your shop:</p>
        <div id="competitors-list-${cat.key}" style="margin-bottom: 20px;">
          <div style="text-align: center; color: #666;">Loading competitors...</div>
        </div>
        <div style="display: flex; justify-content: center; gap: 10px;">
          <button id="refresh-competitors-${cat.key}" style="padding: 8px 16px; background: #5bc0de; color: white; border: none; border-radius: 4px; cursor: pointer;">
            🔄 Refresh
          </button>
          <button id="close-competitors-${cat.key}" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
            ✕ Close
          </button>
        </div>
        <div style="margin-top: 15px; font-size: 12px; color: #666;">
          Auto-refresh every 1 minute • Monitoring until normal undercutting is possible
        </div>
      </div>
    `;

    // Add event listeners
    const refreshBtn = overlay.querySelector(`#refresh-competitors-${cat.key}`);
    const closeBtn = overlay.querySelector(`#close-competitors-${cat.key}`);
    
    refreshBtn.addEventListener('click', () => updateCompetitorsList(cat));
    closeBtn.addEventListener('click', () => hideCompetitorUI(cat));

    // Initial load
    updateCompetitorsList(cat);
    
    // Start monitoring if not already started
    if (!st.competitorMonitoringInterval) {
      st.competitorMonitoringInterval = setInterval(() => {
        if (st.wrapper.querySelector('.competitor-overlay')?.style.display !== 'none') {
          updateCompetitorsList(cat);
        }
      }, 60000); // 1 minute
    }

    st.competitorUIVisible = true;
  }

  async function updateCompetitorsList(cat) {
    const st = state.get(cat.key);
    if (!st) return;

    const listContainer = st.wrapper.querySelector(`#competitors-list-${cat.key}`);
    if (!listContainer) return;

    listContainer.innerHTML = '<div style="text-align: center; color: #666;">Loading competitors...</div>';

    const competitors = await fetchRetailCompetitors();
    
    if (competitors.length === 0) {
      listContainer.innerHTML = '<div style="text-align: center; color: #d9534f;">Failed to load competitors</div>';
      return;
    }

    // Check if we can now undercut the lowest price
    const lowestPrice = competitors.length > 0 ? competitors[0].price : 0;
    if (lowestPrice >= minRetail) {
      logInfo('Lowest competitor price is now above minimum - resuming normal undercutting');
      hideCompetitorUI(cat);
      return;
    }

    let competitorsHTML = '<div style="text-align: left; display: inline-block;">';
    competitors.forEach((comp, index) => {
      const undercutPrice = Math.max(comp.price - UND_STEP, minRetail);
      const stockText = comp.stock >= 1000 ? `${Math.round(comp.stock / 1000)}k` : comp.stock.toString();
      
      competitorsHTML += `
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9; min-width: 350px;">
          <div style="flex: 1;">
            <strong>${index + 1}. ${comp.username}</strong> - $${comp.price.toFixed(5)} - ${stockText}
          </div>
          <button class="undercut-competitor-btn" data-price="${undercutPrice}" data-competitor="${comp.username}" 
                  style="padding: 4px 8px; background: #5cb85c; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">
            Undercut (${undercutPrice.toFixed(5)})
          </button>
        </div>
      `;
    });
    competitorsHTML += '</div>';

    listContainer.innerHTML = competitorsHTML;

    // Add click handlers for undercut buttons
    listContainer.querySelectorAll('.undercut-competitor-btn').forEach(btn => {
      btn.addEventListener('click', async () => {
        const price = parseFloat(btn.dataset.price);
        const competitor = btn.dataset.competitor;
        await executeCompetitorUndercut(cat, price, competitor);
      });
    });
  }

  async function executeCompetitorUndercut(cat, targetPrice, competitorName) {
    const st = state.get(cat.key);
    if (!st) return;

    logInfo(`Starting competitor undercut: ${competitorName} at $${targetPrice.toFixed(5)}`);
    
    // Get all checked realms
    const checkedRows = Array.from(st.rows.values()).filter(row => row.skipBox.checked);
    if (checkedRows.length === 0) {
      logWarning('No realms selected for competitor undercutting');
      return;
    }

    // Disable all competitor buttons during operation
    st.wrapper.querySelectorAll('.undercut-competitor-btn').forEach(btn => {
      btn.disabled = true;
      btn.textContent = '...';
    });

    let successCount = 0;
    
    // Set prices for all checked realms with delay
    for (let i = 0; i < checkedRows.length; i++) {
      const row = checkedRows[i];
      const en = row.entryRef;
      
      if (await setManualPrice(cat, en, row, targetPrice)) {
        successCount++;
        logSuccess(`Competitor undercut: ${en.realm}${en.faction ? ` ${en.faction}` : ''} = ${targetPrice.toFixed(5)}`);
      }
      
      // Add delay between setting prices (except for the last one)
      if (i < checkedRows.length - 1) {
        await new Promise(resolve => setTimeout(resolve, randDelay() * 1000));
      }
    }

    // Re-enable buttons
    st.wrapper.querySelectorAll('.undercut-competitor-btn').forEach(btn => {
      btn.disabled = false;
      const price = parseFloat(btn.dataset.price);
      btn.textContent = `Undercut (${price.toFixed(5)})`;
    });

    logInfo(`Competitor undercut completed: ${successCount}/${checkedRows.length} realms updated`);
    
    // Wait for all prices to be set, then refresh "You" prices once
    setTimeout(() => {
      logInfo(`Verifying competitor undercut prices for ${checkedRows.length} Retail realms...`);
      fetchAndCompare(cat);
      // After verifying, refresh the competitor list to see our position
      setTimeout(() => updateCompetitorsList(cat), 2000);
    }, 5000);
  }

  function hideCompetitorUI(cat) {
    const st = state.get(cat.key);
    if (!st) return;

    const overlay = st.wrapper.querySelector('.competitor-overlay');
    if (overlay) {
      overlay.style.display = 'none';
    }
    
    st.competitorUIVisible = false;
  }

  function showCompetitorUI(cat) {
    const st = state.get(cat.key);
    if (!st) return;

    const overlay = st.wrapper.querySelector('.competitor-overlay');
    if (overlay) {
      overlay.style.display = 'flex';
    } else {
      createCompetitorSelectionUI(cat);
    }
    
    st.competitorUIVisible = true;
  }

  // ───────── TARGETED PRICE REFRESH ─────────────────────────────────
  async function refreshSpecificRealmPrice(cat, entryId, realmName) {
    try {
      logInfo(`Refreshing "You" price for ${realmName}...`);
      
      const u = new URL('/api/predefinedOffers/me/', API_BASE);
      u.searchParams.set('gameId', cat.params.gameId);
      u.searchParams.set('category', 'Currency');
      u.searchParams.set('pageSize', '40');
      u.searchParams.set('pageIndex', '1');
      u.searchParams.set('tradeEnvironmentId', entryId);
      
      const r = await fetch(u, {credentials: 'include'});
      
      if (r.status === 429) {
        logWarning(`Rate limited refreshing ${realmName}. Will try again later.`);
        return false;
      }
      
      if (r.ok) {
        const d = await r.json();
        const ps = (d.results || []).map(x => x.pricePerUnit.amount).filter(n => typeof n === 'number');
        const yourPrice = ps.length ? Math.min(...ps) : null;
        
        // Update cache
        setYourPrice(cat.key, entryId, yourPrice);
        
        // Update UI immediately
        const st = state.get(cat.key);
        if (st) {
          const key = `${cat.key}:${entryId}`;
          const row = st.rows.get(key);
          if (row) {
            row.youCell.textContent = yourPrice != null ? yourPrice.toFixed(5) : '—';
            row.youCell.className = 'you-price-fresh';
            if (row.entryRef) row.entryRef.you = yourPrice;
            logSuccess(`Updated "You" price for ${realmName}: ${yourPrice ? yourPrice.toFixed(5) : 'None'}`);
          }
        }
        
        return true;
      }
    } catch (error) {
      logError(`Error refreshing price for ${realmName}: ${error.message}`);
    }
    return false;
  }

  // ───────── CONTROL ───────────────────────────────────────
})();
