(() => {
  const API_BASE = location.origin;
  const ACCOUNT_NAME = 'Bonyadi';
  const CHECK_INTERVAL = 10000; // Check for instructions every 10 seconds
  const UND_STEP = 0.00001;
  
  let isProcessing = false;
  let checkInterval = null;
  let UI = {};
  
  // Enhanced logging system
  const logs = [];
  const MAX_LOGS = 200;
  
  function addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString('en-US', { hour12: false }) + '.' + String(new Date().getMilliseconds()).padStart(3, '0');
    const logEntry = {
      timestamp,
      type,
      message,
      fullTimestamp: new Date().toISOString()
    };
    
    logs.unshift(logEntry);
    if (logs.length > MAX_LOGS) logs.pop();
    
    // Update UI if available
    updateLogDisplay();
    
    // Store in Chrome storage
    chrome.storage.local.get('bonyadi_logs', res => {
      const storedLogs = res.bonyadi_logs || [];
      storedLogs.unshift(`${timestamp} - [${type.toUpperCase()}] ${message}`);
      while (storedLogs.length > 500) storedLogs.pop();
      chrome.storage.local.set({ bonyadi_logs: storedLogs });
    });
    
    console.log(`[Bonyadi ${type.toUpperCase()}] ${message}`);
  }
  
  function logInfo(message) { addLog(message, 'info'); }
  function logSuccess(message) { addLog(message, 'success'); }
  function logError(message) { addLog(message, 'error'); }
  function logWarning(message) { addLog(message, 'warning'); }
  
  // Create minimal UI
  function createUI() {
    const panel = document.createElement('div');
    panel.id = 'bonyadi-panel';
    Object.assign(panel.style, {
      position: 'fixed', top: '20px', right: '20px',
      background: '#fff', border: '2px solid #007bff', borderRadius: '8px',
      boxShadow: '0 2px 6px rgba(0,0,0,0.2)', fontFamily: 'sans-serif',
      fontSize: '12px', userSelect: 'none', width: '350px', zIndex: 9999
    });
    
    panel.innerHTML = `
      <div style="cursor:move;padding:8px;background:#007bff;color:white;display:flex;align-items:center">
        <strong>El-Cut Secondary (${ACCOUNT_NAME})</strong>
        <div style="margin-left:auto;display:flex;align-items:center;gap:10px">
          <span id="status-indicator" style="padding:2px 6px;background:rgba(255,255,255,0.2);border-radius:3px">Idle</span>
          <button id="toggle-monitoring" style="padding:2px 6px;background:white;color:#007bff;border:none;border-radius:3px;cursor:pointer">Start</button>
        </div>
      </div>
      <div id="panel-body" style="padding:8px">
        <div style="margin-bottom:8px">
          <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:4px">
            <strong>Activity Log</strong>
            <div>
              <button id="clear-logs" style="padding:2px 6px;font-size:10px">Clear</button>
              <button id="export-logs" style="padding:2px 6px;font-size:10px">Export</button>
            </div>
          </div>
          <div id="log-container" style="height:200px;overflow-y:auto;border:1px solid #ddd;padding:4px;background:#f9f9f9;font-family:monospace;font-size:11px">
            <!-- Logs will be populated here -->
          </div>
        </div>
        <div style="font-size:11px;color:#666">
          <div>Check Interval: ${CHECK_INTERVAL/1000}s</div>
          <div>Account: <strong>${ACCOUNT_NAME}</strong></div>
          <div>Status: <span id="detailed-status">Waiting for instructions...</span></div>
        </div>
      </div>
    `;
    
    document.body.appendChild(panel);
    
    // Store UI references
    UI.panel = panel;
    UI.statusIndicator = panel.querySelector('#status-indicator');
    UI.toggleButton = panel.querySelector('#toggle-monitoring');
    UI.logContainer = panel.querySelector('#log-container');
    UI.detailedStatus = panel.querySelector('#detailed-status');
    UI.clearLogs = panel.querySelector('#clear-logs');
    UI.exportLogs = panel.querySelector('#export-logs');
    
    // Make draggable
    makeDraggable(panel, panel.querySelector('div'));
    
    // Event listeners
    UI.toggleButton.addEventListener('click', toggleMonitoring);
    UI.clearLogs.addEventListener('click', clearLogs);
    UI.exportLogs.addEventListener('click', exportLogs);
    
    // Double-click to minimize
    panel.querySelector('div').addEventListener('dblclick', () => {
      const body = panel.querySelector('#panel-body');
      body.style.display = body.style.display === 'none' ? 'block' : 'none';
    });
    
    logInfo('Secondary extension UI initialized');
  }
  
  function makeDraggable(element, handle) {
    let isDragging = false;
    let currentX, currentY, initialX, initialY;
    
    handle.addEventListener('mousedown', (e) => {
      initialX = e.clientX - element.offsetLeft;
      initialY = e.clientY - element.offsetTop;
      isDragging = true;
    });
    
    document.addEventListener('mousemove', (e) => {
      if (isDragging) {
        currentX = e.clientX - initialX;
        currentY = e.clientY - initialY;
        element.style.left = currentX + 'px';
        element.style.top = currentY + 'px';
      }
    });
    
    document.addEventListener('mouseup', () => {
      isDragging = false;
    });
  }
  
  function updateLogDisplay() {
    if (!UI.logContainer) return;
    
    UI.logContainer.innerHTML = logs.slice(0, 50).map(log => {
      const colorMap = {
        info: '#333',
        success: '#28a745',
        warning: '#ffc107',
        error: '#dc3545'
      };
      
      return `<div style="color:${colorMap[log.type] || '#333'};margin-bottom:2px">
        <span style="color:#888">${log.timestamp}</span> ${log.message}
      </div>`;
    }).join('');
    
    // Auto-scroll to top for new messages
    UI.logContainer.scrollTop = 0;
  }
  
  function updateStatus(status, detailed = null) {
    if (UI.statusIndicator) {
      UI.statusIndicator.textContent = status;
      
      // Color coding
      const colors = {
        'Idle': '#6c757d',
        'Checking': '#17a2b8',
        'Processing': '#ffc107',
        'Success': '#28a745',
        'Error': '#dc3545'
      };
      
      UI.statusIndicator.style.background = colors[status] || '#6c757d';
    }
    
    if (UI.detailedStatus && detailed) {
      UI.detailedStatus.textContent = detailed;
    }
  }
  
  function toggleMonitoring() {
    if (checkInterval) {
      stopMonitoring();
    } else {
      startMonitoring();
    }
  }
  
  function startMonitoring() {
    if (checkInterval) return;
    
    logInfo('Starting instruction monitoring...');
    updateStatus('Checking', 'Monitoring started - checking for instructions...');
    UI.toggleButton.textContent = 'Stop';
    UI.toggleButton.style.background = '#dc3545';
    UI.toggleButton.style.color = 'white';
    
    // Check immediately, then set interval
    checkForInstructions();
    checkInterval = setInterval(checkForInstructions, CHECK_INTERVAL);
  }
  
  function stopMonitoring() {
    if (checkInterval) {
      clearInterval(checkInterval);
      checkInterval = null;
    }
    
    logInfo('Instruction monitoring stopped');
    updateStatus('Idle', 'Monitoring stopped');
    UI.toggleButton.textContent = 'Start';
    UI.toggleButton.style.background = 'white';
    UI.toggleButton.style.color = '#007bff';
  }
  
  function clearLogs() {
    logs.length = 0;
    updateLogDisplay();
    chrome.storage.local.set({ bonyadi_logs: [] });
    logInfo('Logs cleared');
  }
  
  function exportLogs() {
    chrome.storage.local.get('bonyadi_logs', res => {
      const storedLogs = res.bonyadi_logs || [];
      const blob = new Blob([storedLogs.join('\n')], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `bonyadi-logs-${new Date().toISOString().replace(/:/g, '-')}.txt`;
      a.click();
      URL.revokeObjectURL(url);
      logInfo('Logs exported to file');
    });
  }
  
  // Main instruction checking function
  async function checkForInstructions() {
    if (isProcessing) {
      logInfo('Still processing previous instruction, skipping check...');
      return;
    }
    
    try {
      updateStatus('Checking', 'Checking for new instructions...');
      
      // Get instructions from Google Sheets via background script
      const response = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({ type: 'fetchInstructions' }, (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        });
      });
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      const data = response.data;
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch instructions');
      }
      
      const instructions = data.instructions || [];
      
      if (instructions.length === 0) {
        updateStatus('Idle', `No pending instructions (last check: ${new Date().toLocaleTimeString()})`);
        return;
      }
      
      logInfo(`Found ${instructions.length} pending instruction(s)`);
      
      // Process the first instruction (FIFO)
      const instruction = instructions[0];
      await processInstruction(instruction);
      
    } catch (error) {
      logError(`Error checking for instructions: ${error.message}`);
      updateStatus('Error', `Check failed: ${error.message}`);
    }
  }
  
  // Process a single instruction
  async function processInstruction(instruction) {
    isProcessing = true;
    
    try {
      updateStatus('Processing', `Processing ${instruction.realm}:${instruction.faction || 'N/A'}`);
      logInfo(`Processing instruction: ${instruction.instruction} for ${instruction.realm}:${instruction.faction || 'N/A'} - Target: ${instruction.targetPrice}`);
      
      // Update status to processing
      await updateInstructionStatus(instruction.id, 'PROCESSING', 'Started processing instruction');
      
      let success = false;
      
      if (instruction.instruction === 'UNDERCUT' || instruction.instruction === 'SET_PRICE') {
        success = await setPriceForRealm(instruction.realm, instruction.faction, instruction.targetPrice);
      } else {
        logWarning(`Unknown instruction type: ${instruction.instruction}`);
        await updateInstructionStatus(instruction.id, 'FAILED', `Unknown instruction type: ${instruction.instruction}`);
        return;
      }
      
      if (success) {
        logSuccess(`Successfully processed instruction for ${instruction.realm}:${instruction.faction || 'N/A'}`);
        updateStatus('Success', `Completed: ${instruction.realm}:${instruction.faction || 'N/A'}`);
        await updateInstructionStatus(instruction.id, 'COMPLETED', 'Price change completed successfully');
      } else {
        logError(`Failed to process instruction for ${instruction.realm}:${instruction.faction || 'N/A'}`);
        updateStatus('Error', `Failed: ${instruction.realm}:${instruction.faction || 'N/A'}`);
        await updateInstructionStatus(instruction.id, 'FAILED', 'Price change failed');
      }
      
    } catch (error) {
      logError(`Error processing instruction: ${error.message}`);
      updateStatus('Error', `Processing error: ${error.message}`);
      await updateInstructionStatus(instruction.id, 'FAILED', `Error: ${error.message}`);
    } finally {
      isProcessing = false;
      
      // Return to checking status after a brief delay
      setTimeout(() => {
        if (checkInterval) {
          updateStatus('Checking', 'Ready for next instruction...');
        }
      }, 2000);
    }
  }

  // Update instruction status in Google Sheets
  async function updateInstructionStatus(rowId, status, message = '') {
    try {
      const response = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({
          type: 'updateStatus',
          rowId: rowId,
          status: status,
          message: message
        }, (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        });
      });

      if (response.error) {
        throw new Error(response.error);
      }

      return response.data.success;
    } catch (error) {
      logError(`Failed to update instruction status: ${error.message}`);
      return false;
    }
  }

  // Set price for a specific realm/faction
  async function setPriceForRealm(realm, faction, targetPrice) {
    try {
      logInfo(`Setting price for ${realm}:${faction || 'N/A'} to ${targetPrice}`);

      // First, get the trade environment ID for this realm/faction
      const entryId = await getTradeEnvironmentId(realm, faction);
      if (!entryId) {
        logError(`Could not find trade environment ID for ${realm}:${faction || 'N/A'}`);
        return false;
      }

      // Get current offers for this realm/faction
      const offers = await getCurrentOffers(entryId);
      if (!offers || offers.length === 0) {
        logError(`No offers found for ${realm}:${faction || 'N/A'}`);
        return false;
      }

      logInfo(`Found ${offers.length} offer(s) to update for ${realm}:${faction || 'N/A'}`);

      // Get XSRF token
      const token = getXSRFToken();
      if (!token) {
        logError('XSRF token not found');
        return false;
      }

      // Update all offers with the new price
      const updatePromises = offers.map(offer =>
        fetch(`${API_BASE}/api/predefinedOffersUser/me/${offer.id}/changePrice/`, {
          method: 'PUT',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'X-XSRF-TOKEN': token
          },
          body: JSON.stringify({
            amount: targetPrice,
            currency: 'USD'
          })
        })
      );

      const results = await Promise.all(updatePromises.map(p =>
        p.catch(error => ({ ok: false, status: 0, error }))
      ));

      // Check for rate limiting
      const has429 = results.some(res => res.status === 429);
      if (has429) {
        logWarning(`Rate limited when changing price for ${realm}:${faction || 'N/A'}`);

        // Report rate limit
        chrome.runtime.sendMessage({
          type: 'reportRateLimit',
          realm: realm,
          faction: faction || ''
        });

        return false;
      }

      // Check for other failures
      const failed = results.filter(res => !res.ok);
      if (failed.length > 0) {
        logError(`Failed to update ${failed.length} offer(s) for ${realm}:${faction || 'N/A'}`);
        return false;
      }

      logSuccess(`Successfully updated price to ${targetPrice} for ${realm}:${faction || 'N/A'}`);
      return true;

    } catch (error) {
      logError(`Error setting price for ${realm}:${faction || 'N/A'}: ${error.message}`);
      return false;
    }
  }

  // Get trade environment ID for realm/faction combination
  async function getTradeEnvironmentId(realm, faction) {
    try {
      // This is a simplified approach - in reality, you might need to fetch from the API
      // For now, we'll try to match against known combinations

      // Try Retail first (gameId: 0)
      let gameId = '0';
      let apiUrl = `${API_BASE}/api/predefinedOffers/game?gameId=${gameId}&category=Currency&pageSize=48&pageIndex=1&tradeEnvironmentValue0=EU`;

      let response = await fetch(apiUrl, { credentials: 'include' });
      if (response.ok) {
        const data = await response.json();
        const results = data.results || [];

        for (const result of results) {
          const offer = result.offer || result;
          const tradeEnvs = offer.tradeEnvironmentValues;
          if (tradeEnvs[1].value === realm && (!faction || tradeEnvs[2].value === faction)) {
            return tradeEnvs[2]?.id || tradeEnvs[1].id;
          }
        }
      }

      // Try other game IDs if not found in Retail
      const gameIds = ['92', '14']; // Anniversary/SOD and Cata
      for (const gId of gameIds) {
        try {
          apiUrl = `${API_BASE}/api/predefinedOffers/game?gameId=${gId}&category=Currency&pageSize=24&pageIndex=1&tradeEnvironmentValue0=EU`;
          response = await fetch(apiUrl, { credentials: 'include' });

          if (response.ok) {
            const data = await response.json();
            const results = data.results || [];

            for (const result of results) {
              const offer = result.offer || result;
              const tradeEnvs = offer.tradeEnvironmentValues;
              if (tradeEnvs[1].value === realm && (!faction || tradeEnvs[2].value === faction)) {
                return tradeEnvs[2]?.id || tradeEnvs[1].id;
              }
            }
          }
        } catch (error) {
          logWarning(`Error checking gameId ${gId}: ${error.message}`);
        }
      }

      return null;
    } catch (error) {
      logError(`Error getting trade environment ID: ${error.message}`);
      return null;
    }
  }

  // Get current offers for a trade environment
  async function getCurrentOffers(entryId) {
    try {
      // Try different game IDs to find the offers
      const gameIds = ['0', '92', '14'];

      for (const gameId of gameIds) {
        try {
          const url = new URL('/api/predefinedOffers/me/', API_BASE);
          url.searchParams.set('gameId', gameId);
          url.searchParams.set('category', 'Currency');
          url.searchParams.set('pageSize', '40');
          url.searchParams.set('pageIndex', '1');
          url.searchParams.set('tradeEnvironmentId', entryId);

          const response = await fetch(url, { credentials: 'include' });

          if (response.status === 429) {
            logWarning('Rate limited when fetching current offers');
            return null;
          }

          if (response.ok) {
            const data = await response.json();
            const offers = data.results || [];
            if (offers.length > 0) {
              return offers;
            }
          }
        } catch (error) {
          logWarning(`Error checking offers for gameId ${gameId}: ${error.message}`);
        }
      }

      return null;
    } catch (error) {
      logError(`Error getting current offers: ${error.message}`);
      return null;
    }
  }

  // Get XSRF token from cookies
  function getXSRFToken() {
    const cookie = document.cookie.split('; ').find(c => c.startsWith('__Host-XSRF-TOKEN='));
    return cookie ? decodeURIComponent(cookie.split('=')[1]) : null;
  }

  // Verify we're operating as the correct account
  async function verifyAccountName() {
    try {
      // Try to get some account-specific information
      const response = await fetch(`${API_BASE}/api/predefinedOffers/me/?gameId=0&category=Currency&pageSize=1&pageIndex=1`, {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        // We can't directly get the account name from this API, but we can verify we're logged in
        logInfo(`Account verification: Successfully authenticated as secondary account`);

        // Additional verification: check if we have any offers that might indicate account name
        if (data.results && data.results.length > 0) {
          logInfo(`Account has ${data.results.length} active offer(s) - account verification successful`);
        }

        return true;
      } else {
        logError(`Account verification failed: ${response.status} ${response.statusText}`);
        return false;
      }
    } catch (error) {
      logError(`Account verification error: ${error.message}`);
      return false;
    }
  }

  // Initialize the extension
  function init() {
    // Only run on Eldorado pages
    if (!location.hostname.includes('eldorado.gg')) {
      return;
    }

    logInfo(`Secondary extension initialized for account: ${ACCOUNT_NAME}`);

    // Verify account
    verifyAccountName();

    // Create UI
    createUI();

    // Load previous logs from storage
    chrome.storage.local.get('bonyadi_logs', res => {
      const storedLogs = res.bonyadi_logs || [];
      storedLogs.slice(0, 20).forEach(logLine => {
        const parts = logLine.split(' - ');
        if (parts.length >= 2) {
          const timestamp = parts[0];
          const message = parts.slice(1).join(' - ');
          let type = 'info';

          if (message.includes('[ERROR]')) type = 'error';
          else if (message.includes('[WARNING]')) type = 'warning';
          else if (message.includes('[SUCCESS]')) type = 'success';

          logs.push({
            timestamp: timestamp,
            type: type,
            message: message.replace(/\[(ERROR|WARNING|SUCCESS|INFO)\]\s*/, ''),
            fullTimestamp: new Date().toISOString()
          });
        }
      });
      updateLogDisplay();
    });

    logInfo('Secondary extension ready - click "Start" to begin monitoring for instructions');
  }

  // Start initialization when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

})();
