# El-cut Extension Prompts History

## June 5, 2025

### Adding Cata Category
```
check my whole code base carefully. need following changes:
- need a new category called "Cata". we got only following 6 realms for Cata category:
Firemaw A- Gehennas H- Golemagg H- Venoxis H- Everlook A- Pyrewood Village A
A stands for alliance and H for horde.
API to get lowest price on each realm is this:
https://www.eldorado.gg/api/predefinedOffers/game?gameId=14&category=Currency&pageSize=24&pageIndex=1&tradeEnvironmentValue0=EU
```

### Fixing Cata Min Prices
```
this is cata., not bad for the first try. following points:
- those errors
- also i dont see the min for each cata realm. we should get the min from the sheet and write in local collumn next to the converted global lowest price as we have now
```

### Fixing Cata Data Issues
```
still no min from sheet. here is the JSON format we get fromt he sheet:
[{"realm":"Dollar","price":79000,"active":true},{"realm":"Spine A","price":980,"active":true},{"realm":"Spine H","price":990,"active":true},{"realm":"Living A","price":140,"active":true},{"realm":"living H","price":150,"active":true},{"realm":"Thunder A","price":1070,"active":true},{"realm":"Thunder H","price":1200,"active":true},{"realm":"Wild A","price":260,"active":false},{"realm":"Era A","price":320,"active":false},{"realm":"Retail","price":3180,"active":true},{"realm":"IGV Kaf","price":0.000047,"active":true},{"realm":"Eldorado Kaf","price":0.0427,"active":true},{"realm":"Retail $","price":81000,"active":true},{"realm":"Omid","notes":[]},{"realm":"M Spine A","notes":["ahmadd6507 ( 9k   just mail )","mohsen_shadow","sajjad03642","doshan3443  8k"]},{"realm":"M Spine H","notes":["yas9004 ( 5/6 k dare )","taghipoor.md","raybod=actionhouse"]},{"realm":"M Living A","notes":["ayhan4081","loverain","angella4738","greenlucky.","sunless84_11301","darkwitcher","sunless84_11301","hamed860","arshiz","dexter9860","miszax.\n100 k dare=09121867761"]},{"realm":"M living H","notes":["shamsodin","ali.229 (badansaz)","soulmatex 25k","michaelk3539   11 k dare"]},{"realm":"M thunder A","notes":["april.1996","shendry924","andico","majid9276","yudi3112","amiraghrab","arash.ha","buny777.","danial1382a_80658","amironset      1.6","Firemaw A","51","1"]},{"realm":"M thunder H","notes":["mephisto1929","mehdi784","hidd3n3lucif3r ( 3300 G mojudi )","vague0175","abolfazl005559","alidanhil         +7K","vague0175      +7K","hidd3n3lucif3r","vague0175","x_unholy_x     6k","Gehennas H","49","1"]},{"realm":"M Wild A","notes":["gabolj2  2500 G","korra4031","aonea1","ferguson0998","alikixaj","kixaj",".mraghil\n09914247292","Golemagg H","57","1"]},{"realm":"M Era A","notes":["ramin","divine.storm","arash.ha","9102811353","yuichirow","amirwanhedaa era H)","Venoxis H","57","1"]},{"realm":"Moonde","notes":["https://www.g2g.com/g2g-user/sale/order/item/1748912299947NGOO-1","miba buyer offline","https://www.g2g.com/g2g-user/sale/order/item/1748870182437Q0ZB-1","miba buyer offline","https://www.g2g.com/g2g-user/sale/order/item/1749043888574YRQJ-1","bonyadi faction eshtebah","Pyrewood Village A","39","6"]}]

"Gehennas H","49","1"
"Venoxis H","57","1"
and other realms are the 6 refrence from the sheet. is there something wrong scraping them?
the min should be based on adding 2 numbers and multiplying with 1000. for example "Gehennas H","49","1" will have min of 50000 for the extension on eldorado
```

### Fixing Issues After Cata Update
```
few things:
- as u see it shows thunder H postponed for 1 min. its been saying this for the past half hour. Thunder H as u see is below min. so its a one time increasing the price based ont he logics. we increase a percentage based on min for each realm. is not it so? we just increase once and then we stay and observe if the lowest price is above the min so we can keep undercutting. but when it says posponing it feels like its changing the price again and again to the percentage above the min. postponing means it does changing the price too often that it hit the limitation for that realm from eldorado. if its so we should fix it . in cases of below min or inactive realm, we do the logic of pricing once and increase the prices for each realm. and then we just wait to see if global lowest price goes above the min so we can continue undercutting. 
- as u see there are 3 realms at the end of the cata category which i did nto want. those 3 white colord realms. just remove them
- idk why it cant get the min for everlook. as u see there is no min on the extension. also the error
- extension generally works ok and does what it has to do but lots of the errors u see int he shots
```

## June 6, 2025

### Improving UI and Retail Category
```
- we need a much better log. make it a very professional log. also it should show the changes live. put very good tools for it
- the retail category is too wide with only 3 collumns. thats waste of space. but it has many rows. so instead of one long sets of rows, put 2 or 3 collumns that each has some rows and collumns for retail. there are 24 realms. we can make it 3 collumns of 8 rows of realms each collumn with 3 cullumn for detail of the realms
```

### Further UI Improvements and Fixing Retail
```
thats our curreent look. its good but following points:
- select all checkbox for retail is for all of its realms. no need a select all checkbox for each of the 3 collumns. 
- logs should have a minimize button
- we have a delay option on the dashboard. i currently choose 3-6. meaning there should be a random delay when auto button works and changes each realms prices. for example if delay is chosen as 4.34 seconds auto will try to change the price of the next realm in 4.34 seconds and then it should choose a new random delay for the next realm. currently it seems it does not count for the delay. i suddenly see lots of realms gets affected by auto. for all categories it should be the same.
- operations are smooth enough now altho retail category seems weird. currently it shows the lowest price for eahc realm in global collumn but when i increase my price, the global price also gets affected and returns weird prices. im not sure what is happening but lets review the API for lowest prices for each realm of the retail category and also to increase number of realms from 24 we had. the following API shows 48 most popular realms and their lowest prices. so instead of 24 we have 48 realms now:
https://www.eldorado.gg/api/predefinedOffers/game?gameId=0&category=Currency&pageSize=48&pageIndex=1&tradeEnvironmentValue0=EU