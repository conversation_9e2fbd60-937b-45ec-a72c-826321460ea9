chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  if (msg.type === 'fetchInstructions') {
    fetch('https://script.google.com/macros/s/YOUR_DUAL_ACCOUNT_SCRIPT_ID/exec?action=getInstructions&account=Bonyadi')
      .then(r => r.ok ? r.json() : { success: false, error: 'Network error' })
      .then(data => sendResponse({ data }))
      .catch(error => sendResponse({ error: error.toString() }));
    return true;
  }
  
  if (msg.type === 'updateStatus') {
    const params = new URLSearchParams({
      action: 'updateStatus',
      rowId: msg.rowId,
      status: msg.status,
      account: 'Bonyadi',
      message: msg.message || ''
    });
    
    fetch(`https://script.google.com/macros/s/YOUR_DUAL_ACCOUNT_SCRIPT_ID/exec?${params}`)
      .then(r => r.ok ? r.json() : { success: false, error: 'Network error' })
      .then(data => sendResponse({ data }))
      .catch(error => sendResponse({ error: error.toString() }));
    return true;
  }
  
  if (msg.type === 'reportRateLimit') {
    const params = new URLSearchParams({
      action: 'reportRateLimit',
      account: 'Bonyadi',
      realm: msg.realm,
      faction: msg.faction || ''
    });
    
    fetch(`https://script.google.com/macros/s/YOUR_DUAL_ACCOUNT_SCRIPT_ID/exec?${params}`)
      .then(r => r.ok ? r.json() : { success: false, error: 'Network error' })
      .then(data => sendResponse({ data }))
      .catch(error => sendResponse({ error: error.toString() }));
    return true;
  }
});
