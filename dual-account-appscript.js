/**
 * Google Apps Script for El-Cut Dual Account Coordination
 * Handles communication between main extension (Miba_Shop) and secondary extension (Bonyadi)
 */

// Configuration
const SHEET_NAME = 'DualAccountCoordination';
const LOG_SHEET_NAME = 'CoordinationLog';
const MAX_LOG_ENTRIES = 1000;

// Account names
const MAIN_ACCOUNT = 'Miba_Shop';
const SECONDARY_ACCOUNT = 'Bonyadi';

// Status constants
const STATUS = {
  IDLE: 'IDLE',
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  RATE_LIMITED: 'RATE_LIMITED'
};

function doGet(e) {
  try {
    const action = e.parameter.action;

    switch (action) {
      case 'setup':
        return setupSheets();
      case 'getInstructions':
        return getInstructions(e.parameter.account);
      case 'updateStatus':
        return updateStatus(e.parameter);
      case 'requestPriceChange':
        return requestPriceChange(e.parameter);
      case 'getAccountStatus':
        return getAccountStatus(e.parameter.account);
      case 'reportRateLimit':
        return reportRateLimit(e.parameter);
      case 'getLogs':
        return getLogs();
      case 'test':
        return testConnection();
      default:
        return ContentService.createTextOutput(JSON.stringify({
          error: 'Invalid action',
          availableActions: ['setup', 'test', 'getInstructions', 'updateStatus', 'requestPriceChange', 'getAccountStatus', 'reportRateLimit', 'getLogs'],
          message: 'Add ?action=setup to initialize the sheets, or ?action=test to test the connection'
        })).setMimeType(ContentService.MimeType.JSON);
    }
  } catch (error) {
    logError('doGet error', error);
    return ContentService.createTextOutput(JSON.stringify({
      error: error.toString()
    })).setMimeType(ContentService.MimeType.JSON);
  }
}

function doPost(e) {
  try {
    const data = JSON.parse(e.postData.contents);
    const action = data.action;
    
    switch (action) {
      case 'bulkUpdateInstructions':
        return bulkUpdateInstructions(data);
      case 'switchActiveAccount':
        return switchActiveAccount(data);
      default:
        return ContentService.createTextOutput(JSON.stringify({
          error: 'Invalid POST action'
        })).setMimeType(ContentService.MimeType.JSON);
    }
  } catch (error) {
    logError('doPost error', error);
    return ContentService.createTextOutput(JSON.stringify({
      error: error.toString()
    })).setMimeType(ContentService.MimeType.JSON);
  }
}

function getInstructions(account) {
  try {
    const sheet = getOrCreateSheet();
    const data = sheet.getDataRange().getValues();
    const headers = data[0];
    
    const instructions = [];
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      const rowData = {};
      headers.forEach((header, index) => {
        rowData[header] = row[index];
      });
      
      // Only return instructions for the requesting account that are pending
      if (rowData.targetAccount === account && rowData.status === STATUS.PENDING) {
        instructions.push({
          id: i + 1, // Row number for reference
          realm: rowData.realm,
          faction: rowData.faction,
          targetPrice: rowData.targetPrice,
          instruction: rowData.instruction,
          timestamp: rowData.timestamp,
          priority: rowData.priority || 'normal'
        });
      }
    }
    
    logActivity(`Retrieved ${instructions.length} instructions for ${account}`);
    
    return ContentService.createTextOutput(JSON.stringify({
      success: true,
      instructions: instructions,
      timestamp: new Date().toISOString()
    })).setMimeType(ContentService.MimeType.JSON);
    
  } catch (error) {
    logError('getInstructions error', error);
    return ContentService.createTextOutput(JSON.stringify({
      error: error.toString()
    })).setMimeType(ContentService.MimeType.JSON);
  }
}

function updateStatus(params) {
  try {
    const sheet = getOrCreateSheet();
    const rowId = parseInt(params.rowId);
    const status = params.status;
    const account = params.account;
    const message = params.message || '';
    
    // Update the status in the specified row
    const statusCol = getColumnIndex(sheet, 'status');
    const lastUpdatedCol = getColumnIndex(sheet, 'lastUpdated');
    const messageCol = getColumnIndex(sheet, 'message');
    
    sheet.getRange(rowId, statusCol).setValue(status);
    sheet.getRange(rowId, lastUpdatedCol).setValue(new Date());
    if (message) {
      sheet.getRange(rowId, messageCol).setValue(message);
    }
    
    logActivity(`Status updated for row ${rowId}: ${status} by ${account}`);
    
    return ContentService.createTextOutput(JSON.stringify({
      success: true,
      message: 'Status updated successfully'
    })).setMimeType(ContentService.MimeType.JSON);
    
  } catch (error) {
    logError('updateStatus error', error);
    return ContentService.createTextOutput(JSON.stringify({
      error: error.toString()
    })).setMimeType(ContentService.MimeType.JSON);
  }
}

function requestPriceChange(params) {
  try {
    const sheet = getOrCreateSheet();
    
    // Add new price change request
    const newRow = [
      params.realm,
      params.faction || '',
      params.targetPrice,
      params.targetAccount,
      params.instruction || 'UNDERCUT',
      STATUS.PENDING,
      new Date(),
      new Date(),
      params.requestedBy || MAIN_ACCOUNT,
      params.priority || 'normal',
      ''  // message column
    ];
    
    sheet.appendRow(newRow);
    
    logActivity(`Price change requested for ${params.realm}:${params.faction || 'N/A'} - Target: ${params.targetPrice} - Account: ${params.targetAccount}`);
    
    return ContentService.createTextOutput(JSON.stringify({
      success: true,
      message: 'Price change request added successfully'
    })).setMimeType(ContentService.MimeType.JSON);
    
  } catch (error) {
    logError('requestPriceChange error', error);
    return ContentService.createTextOutput(JSON.stringify({
      error: error.toString()
    })).setMimeType(ContentService.MimeType.JSON);
  }
}

function getAccountStatus(account) {
  try {
    const sheet = getOrCreateSheet();
    const data = sheet.getDataRange().getValues();
    const headers = data[0];
    
    const accountData = {
      account: account,
      pendingInstructions: 0,
      processingInstructions: 0,
      completedToday: 0,
      failedToday: 0,
      rateLimitedUntil: null,
      lastActivity: null
    };
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      const rowData = {};
      headers.forEach((header, index) => {
        rowData[header] = row[index];
      });
      
      if (rowData.targetAccount === account) {
        const timestamp = new Date(rowData.timestamp);
        
        switch (rowData.status) {
          case STATUS.PENDING:
            accountData.pendingInstructions++;
            break;
          case STATUS.PROCESSING:
            accountData.processingInstructions++;
            break;
          case STATUS.COMPLETED:
            if (timestamp >= today) accountData.completedToday++;
            break;
          case STATUS.FAILED:
            if (timestamp >= today) accountData.failedToday++;
            break;
          case STATUS.RATE_LIMITED:
            // Find the most recent rate limit
            if (!accountData.rateLimitedUntil || timestamp > new Date(accountData.rateLimitedUntil)) {
              // Assume 5-minute rate limit from the timestamp
              accountData.rateLimitedUntil = new Date(timestamp.getTime() + 5 * 60 * 1000);
            }
            break;
        }
        
        if (!accountData.lastActivity || timestamp > new Date(accountData.lastActivity)) {
          accountData.lastActivity = timestamp;
        }
      }
    }
    
    return ContentService.createTextOutput(JSON.stringify({
      success: true,
      accountStatus: accountData,
      timestamp: new Date().toISOString()
    })).setMimeType(ContentService.MimeType.JSON);
    
  } catch (error) {
    logError('getAccountStatus error', error);
    return ContentService.createTextOutput(JSON.stringify({
      error: error.toString()
    })).setMimeType(ContentService.MimeType.JSON);
  }
}

function reportRateLimit(params) {
  try {
    const sheet = getOrCreateSheet();
    const account = params.account;
    const realm = params.realm;
    const faction = params.faction || '';
    
    // Add rate limit report
    const newRow = [
      realm,
      faction,
      0, // targetPrice - not applicable for rate limit
      account,
      'RATE_LIMIT_DETECTED',
      STATUS.RATE_LIMITED,
      new Date(),
      new Date(),
      account,
      'high', // priority
      `Rate limited detected at ${new Date().toISOString()}`
    ];
    
    sheet.appendRow(newRow);
    
    logActivity(`Rate limit reported for ${account} on ${realm}:${faction}`);
    
    return ContentService.createTextOutput(JSON.stringify({
      success: true,
      message: 'Rate limit reported successfully'
    })).setMimeType(ContentService.MimeType.JSON);
    
  } catch (error) {
    logError('reportRateLimit error', error);
    return ContentService.createTextOutput(JSON.stringify({
      error: error.toString()
    })).setMimeType(ContentService.MimeType.JSON);
  }
}

function getOrCreateSheet() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  let sheet = ss.getSheetByName(SHEET_NAME);

  if (!sheet) {
    sheet = ss.insertSheet(SHEET_NAME);
  }

  // Always ensure headers are present (in case sheet exists but is empty)
  if (sheet.getLastRow() === 0) {
    const headers = [
      'realm', 'faction', 'targetPrice', 'targetAccount', 'instruction',
      'status', 'timestamp', 'lastUpdated', 'requestedBy', 'priority', 'message'
    ];
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');

    // Set column widths for better readability
    sheet.setColumnWidth(1, 120); // realm
    sheet.setColumnWidth(2, 80);  // faction
    sheet.setColumnWidth(3, 100); // targetPrice
    sheet.setColumnWidth(4, 100); // targetAccount
    sheet.setColumnWidth(5, 120); // instruction
    sheet.setColumnWidth(6, 100); // status
    sheet.setColumnWidth(7, 150); // timestamp
    sheet.setColumnWidth(8, 150); // lastUpdated
    sheet.setColumnWidth(9, 100); // requestedBy
    sheet.setColumnWidth(10, 80); // priority
    sheet.setColumnWidth(11, 200); // message
  }

  return sheet;
}

function getColumnIndex(sheet, columnName) {
  const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
  return headers.indexOf(columnName) + 1;
}

function logActivity(message) {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    let logSheet = ss.getSheetByName(LOG_SHEET_NAME);
    
    if (!logSheet) {
      logSheet = ss.insertSheet(LOG_SHEET_NAME);
      logSheet.getRange(1, 1, 1, 3).setValues([['Timestamp', 'Type', 'Message']]);
      logSheet.getRange(1, 1, 1, 3).setFontWeight('bold');
    }
    
    logSheet.appendRow([new Date(), 'INFO', message]);
    
    // Keep only the last MAX_LOG_ENTRIES
    if (logSheet.getLastRow() > MAX_LOG_ENTRIES + 1) {
      logSheet.deleteRows(2, logSheet.getLastRow() - MAX_LOG_ENTRIES - 1);
    }
  } catch (error) {
    console.error('Logging error:', error);
  }
}

function logError(context, error) {
  const message = `${context}: ${error.toString()}`;
  console.error(message);
  logActivity(`ERROR - ${message}`);
}

function getLogs() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const logSheet = ss.getSheetByName(LOG_SHEET_NAME);
    
    if (!logSheet) {
      return ContentService.createTextOutput(JSON.stringify({
        success: true,
        logs: []
      })).setMimeType(ContentService.MimeType.JSON);
    }
    
    const data = logSheet.getDataRange().getValues();
    const logs = data.slice(1).map(row => ({
      timestamp: row[0],
      type: row[1],
      message: row[2]
    }));
    
    return ContentService.createTextOutput(JSON.stringify({
      success: true,
      logs: logs.slice(-100) // Return last 100 entries
    })).setMimeType(ContentService.MimeType.JSON);
    
  } catch (error) {
    logError('getLogs error', error);
    return ContentService.createTextOutput(JSON.stringify({
      error: error.toString()
    })).setMimeType(ContentService.MimeType.JSON);
  }
}

// Setup function to initialize sheets
function setupSheets() {
  try {
    // Initialize main coordination sheet
    const mainSheet = getOrCreateSheet();

    // Initialize log sheet
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    let logSheet = ss.getSheetByName(LOG_SHEET_NAME);

    if (!logSheet) {
      logSheet = ss.insertSheet(LOG_SHEET_NAME);
    }

    if (logSheet.getLastRow() === 0) {
      logSheet.getRange(1, 1, 1, 3).setValues([['Timestamp', 'Type', 'Message']]);
      logSheet.getRange(1, 1, 1, 3).setFontWeight('bold');
      logSheet.setColumnWidth(1, 150); // Timestamp
      logSheet.setColumnWidth(2, 80);  // Type
      logSheet.setColumnWidth(3, 400); // Message
    }

    logActivity('Sheets initialized successfully');

    return ContentService.createTextOutput(JSON.stringify({
      success: true,
      message: 'Sheets initialized successfully',
      mainSheet: SHEET_NAME,
      logSheet: LOG_SHEET_NAME,
      spreadsheetUrl: ss.getUrl()
    })).setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    logError('setupSheets error', error);
    return ContentService.createTextOutput(JSON.stringify({
      error: error.toString()
    })).setMimeType(ContentService.MimeType.JSON);
  }
}

// Test connection function
function testConnection() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const timestamp = new Date().toISOString();

    logActivity(`Test connection successful at ${timestamp}`);

    return ContentService.createTextOutput(JSON.stringify({
      success: true,
      message: 'Connection test successful',
      timestamp: timestamp,
      spreadsheetName: ss.getName(),
      spreadsheetUrl: ss.getUrl(),
      availableSheets: ss.getSheets().map(sheet => sheet.getName())
    })).setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    return ContentService.createTextOutput(JSON.stringify({
      error: error.toString(),
      message: 'Connection test failed'
    })).setMimeType(ContentService.MimeType.JSON);
  }
}
