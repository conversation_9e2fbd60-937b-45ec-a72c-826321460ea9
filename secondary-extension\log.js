function refreshLogs() {
  chrome.storage.local.get('bonyadi_logs', res => {
    const logs = res.bonyadi_logs || [];
    const container = document.getElementById('log-container');
    
    container.innerHTML = logs.map(log => {
      let className = 'info';
      if (log.includes('[ERROR]')) className = 'error';
      else if (log.includes('[WARNING]')) className = 'warning';
      else if (log.includes('[SUCCESS]')) className = 'success';
      
      return `<div class="log-entry ${className}">${log}</div>`;
    }).join('');
    
    // Auto-scroll to bottom
    container.scrollTop = container.scrollHeight;
  });
}

function clearLogs() {
  if (confirm('Are you sure you want to clear all logs?')) {
    chrome.storage.local.set({ bonyadi_logs: [] }, () => {
      refreshLogs();
    });
  }
}

function exportLogs() {
  chrome.storage.local.get('bonyadi_logs', res => {
    const logs = res.bonyadi_logs || [];
    const blob = new Blob([logs.join('\n')], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bonyadi-logs-${new Date().toISOString().replace(/:/g, '-')}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  });
}

// Auto-refresh every 5 seconds
setInterval(refreshLogs, 5000);

// Initial load
refreshLogs();
