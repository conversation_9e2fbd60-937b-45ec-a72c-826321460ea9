# El-Cut Dual Account Setup Guide

This guide will help you set up the dual-account system for your El-Cut extension to handle Eldorado's rate limiting by coordinating between your main account (Miba_Shop) and secondary account (Bonyadi).

## Overview

The dual-account system works as follows:
- **Main Extension (Miba_Shop)**: Monitors all realms, makes 4-5 price changes, then switches to secondary account
- **Secondary Extension (Bonyadi)**: Lightweight extension that only responds to instructions from main extension
- **Google Sheets**: Communication layer between both extensions
- **Smart Switching**: Automatically switches accounts when rate limited or after max changes

## Step 1: Set Up Google Apps Script

1. **Create a new Google Apps Script project:**
   - Go to https://script.google.com/
   - Click "New Project"
   - Replace the default code with the content from `dual-account-appscript.js`

2. **Deploy the script:**
   - Click "Deploy" → "New deployment"
   - Choose type: "Web app"
   - Execute as: "Me"
   - Who has access: "Anyone" (or "Anyone with Google account" for more security)
   - Click "Deploy"
   - Copy the deployment URL - you'll need this for both extensions

3. **Set up the Google Sheet:**
   - The script will automatically create sheets named "DualAccountCoordination" and "CoordinationLog"
   - You can also manually create a sheet and run the script once to initialize the structure

## Step 2: Update Extension URLs

1. **Update main extension (content.js):**
   - Find line 6: `const DUAL_ACCOUNT_URL = 'https://script.google.com/macros/s/YOUR_DUAL_ACCOUNT_SCRIPT_ID/exec';`
   - Replace `YOUR_DUAL_ACCOUNT_SCRIPT_ID` with your actual script deployment URL

2. **Update secondary extension:**
   - In `secondary-extension/manifest.json`, update the host_permissions URL
   - In `secondary-extension/background.js`, replace all instances of `YOUR_DUAL_ACCOUNT_SCRIPT_ID` with your script URL

## Step 3: Install Extensions

### Main Extension (Miba_Shop Account)
1. Open Chrome with your main account (Miba_Shop)
2. Go to `chrome://extensions/`
3. Enable "Developer mode"
4. Click "Load unpacked" and select your main extension folder
5. The extension should appear with dual-account controls

### Secondary Extension (Bonyadi Account)
1. Open a different Chrome browser or Chrome profile with your Bonyadi account
2. Go to `chrome://extensions/`
3. Enable "Developer mode"
4. Click "Load unpacked" and select the `secondary-extension` folder
5. The secondary extension should appear with a blue interface

## Step 4: Configure and Test

### Main Extension Setup:
1. Navigate to Eldorado.gg with your Miba_Shop account
2. The extension panel should show dual-account controls
3. Check "Enable Dual Account" checkbox
4. Click "Check Status" to verify communication with Google Sheets
5. Enable your desired categories (Retail, Anniversary, etc.)

### Secondary Extension Setup:
1. Navigate to Eldorado.gg with your Bonyadi account
2. The secondary extension panel should appear on the right side
3. Click "Start" to begin monitoring for instructions
4. The status should show "Checking" and logs should appear

### Testing the System:
1. In the main extension, enable a category and start auto-undercutting
2. Watch the logs in both extensions
3. After 4-5 price changes, the main extension should delegate to the secondary
4. The secondary extension should receive and execute the instruction
5. Both extensions should log all activities

## Step 5: Monitoring and Troubleshooting

### Main Extension Monitoring:
- **Current Account Display**: Shows which account is currently active
- **Secondary Status**: Shows if Bonyadi extension is online
- **Enhanced Activity Log**: Shows all coordination activities
- **Dual Account Controls**: Enable/disable coordination and check status

### Secondary Extension Monitoring:
- **Status Indicator**: Shows current state (Idle, Checking, Processing, etc.)
- **Activity Log**: Shows all received instructions and actions taken
- **Account Verification**: Logs confirm it's operating as Bonyadi account

### Common Issues and Solutions:

1. **"Secondary: Offline" Status:**
   - Ensure Bonyadi extension is running and "Start" is clicked
   - Check that both extensions use the same Google Apps Script URL
   - Verify Google Apps Script is deployed and accessible

2. **Rate Limiting Still Occurring:**
   - Check that "Enable Dual Account" is checked in main extension
   - Verify the secondary extension is receiving instructions
   - Monitor logs to see if account switching is happening

3. **Price Changes Not Executing:**
   - Ensure both accounts are logged into Eldorado.gg
   - Check XSRF tokens are available (refresh pages if needed)
   - Verify trade environment IDs are being found correctly

4. **Google Sheets Communication Errors:**
   - Check Google Apps Script deployment URL is correct
   - Ensure script has proper permissions
   - Check browser console for network errors

## Step 6: Advanced Configuration

### Adjusting Switch Frequency:
In `content.js`, modify `MAX_CHANGES_PER_ACCOUNT` (default: 4) to change how many price changes occur before switching accounts.

### Rate Limit Cooldown:
Modify `RATE_LIMIT_COOLDOWN` (default: 5 minutes) to adjust how long to wait after detecting rate limiting.

### Check Interval:
In secondary extension, modify `CHECK_INTERVAL` (default: 10 seconds) to change how often it checks for new instructions.

## Expected Behavior

### Normal Operation:
1. Main extension monitors all realms
2. Makes 4-5 price changes with Miba_Shop account
3. Switches to Bonyadi account for next changes
4. Secondary extension executes price changes for Bonyadi
5. Process repeats, alternating between accounts

### Rate Limit Handling:
1. When rate limited, immediately switches to other account
2. Reports rate limit to coordination system
3. Waits 5 minutes before using rate-limited account again
4. Continues operation with non-limited account

### Account Verification:
- Both extensions log which account they're operating as
- Main extension verifies secondary account is responding
- Logs show successful coordination between accounts

## Security Notes

- Keep your Google Apps Script URL private
- Consider using "Anyone with Google account" access instead of "Anyone"
- Monitor logs regularly for any suspicious activity
- Both extensions only operate on Eldorado.gg domains

## Support

If you encounter issues:
1. Check browser console for errors
2. Review extension logs in both extensions
3. Verify Google Apps Script execution transcript
4. Ensure both accounts are properly logged into Eldorado.gg
5. Test with a single realm first before enabling all categories
